﻿@model DKyThucTap.Models.Position

@{
    ViewData["Title"] = "Chi tiết vị trí tuyển dụng";
    Layout = "~/Views/Shared/_Layout_Admin.cshtml";
    var isExpired = Model.ApplicationDeadline.HasValue &&
                   Model.ApplicationDeadline.Value < DateOnly.FromDateTime(DateTime.Now);
    var applicationCount = Model.Applications?.Count() ?? 0;
}

<!-- Breadcrumb -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Chi tiết vị trí tuyển dụng</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("AdminDashboard", "AdminHome", new { area = "Admin" })">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="@Url.Action("Index", "Positions", new { area = "Admin" })">Quản lý vị trí</a>
                    </li>
                    <li class="breadcrumb-item active">Chi tiết</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Position Info -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-briefcase"></i>
                    Thông tin vị trí
                </h3>
                <div class="card-tools">
                    @if (Model.IsActive == true)
                    {
                        @if (isExpired)
                        {
                            <span class="badge badge-warning badge-lg">
                                <i class="fas fa-calendar-times"></i> Đã hết hạn
                            </span>
                        }
                        else
                        {
                            <span class="badge badge-success badge-lg">
                                <i class="fas fa-check-circle"></i> Đang hoạt động
                            </span>
                        }
                    }
                    else
                    {
                        <span class="badge badge-danger badge-lg">
                            <i class="fas fa-pause-circle"></i> Đã khóa
                        </span>
                    }
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Tiêu đề:</dt>
                            <dd class="col-sm-7">
                                <strong class="text-primary">@Model.Title</strong>
                            </dd>

                            <dt class="col-sm-5">Công ty:</dt>
                            <dd class="col-sm-7">
                                @if (Model.Company != null)
                                {
                                    <a href="@Url.Action("Details", "Companies", new { area = "Admin", id = Model.Company.CompanyId })"
                                       class="text-info">
                                        <i class="fas fa-building"></i> @Model.Company.Name
                                    </a>
                                }
                                else
                                {
                                    <span class="text-muted">N/A</span>
                                }
                            </dd>

                            <dt class="col-sm-5">Loại vị trí:</dt>
                            <dd class="col-sm-7">
                                @if (!string.IsNullOrEmpty(Model.PositionType))
                                {
                                    <span class="badge badge-secondary">@Model.PositionType</span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa xác định</span>
                                }
                            </dd>

                            <dt class="col-sm-5">Danh mục:</dt>
                            <dd class="col-sm-7">
                                @if (Model.Category != null)
                                {
                                    <span class="badge badge-info">@Model.Category.CategoryName</span>
                                }
                                else
                                {
                                    <span class="text-muted">Chưa phân loại</span>
                                }
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Địa điểm:</dt>
                            <dd class="col-sm-7">
                                @if (Model.IsRemote == true)
                                {
                                    <span class="badge badge-info">
                                        <i class="fas fa-home"></i> Remote
                                    </span>
                                }
                                else if (!string.IsNullOrEmpty(Model.Location))
                                {
                                    <i class="fas fa-map-marker-alt text-muted"></i> 
                                    @Model.Location
                                }
                                else
                                {
                                    <span class="text-muted">Chưa xác định</span>
                                }
                            </dd>

                            <dt class="col-sm-5">Mức lương:</dt>
                            <dd class="col-sm-7">
                                @if (!string.IsNullOrEmpty(Model.SalaryRange))
                                {
                                    <span class="text-success">
                                        <i class="fas fa-money-bill-wave"></i> @Model.SalaryRange
                                    </span>
                                }
                                else
                                {
                                    <span class="text-muted">Thỏa thuận</span>
                                }
                            </dd>

                            <dt class="col-sm-5">Hạn nộp:</dt>
                            <dd class="col-sm-7">
                                @if (Model.ApplicationDeadline.HasValue)
                                {
                                    <span class="@(isExpired ? "text-danger" : "text-info")">
                                        <i class="fas fa-calendar-alt"></i>
                                        @Model.ApplicationDeadline.Value.ToString("dd/MM/yyyy")
                                        @if (isExpired)
                                        {
                                            <br>
                                    
                                            <small class="text-danger">Đã hết hạn</small>
                                        }
                                    </span>
                                }
                                else
                                {
                                    <span class="text-muted">Không giới hạn</span>
                                }
                            </dd>

                            <dt class="col-sm-5">Ngày tạo:</dt>
                            <dd class="col-sm-7">
                                @if (Model.CreatedAt.HasValue)
                                {
                                    <span class="text-info">@Model.CreatedAt.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                }
                                else
                                {
                                    <span class="text-muted">N/A</span>
                                }
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <hr>
                    <div>
                        <h6 class="font-weight-bold text-gray-800">Mô tả công việc:</h6>
                        <div class="text-gray-900" style="white-space: pre-line;">@Model.Description</div>
                    </div>
                }
            </div>
        </div>

        <!-- Skills Required -->
        @if (Model.PositionSkills != null && Model.PositionSkills.Any())
        {
            <div class="card card-info">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tools"></i>
                        Kỹ năng yêu cầu
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var positionSkill in Model.PositionSkills)
                        {
                            <div class="col-md-4 mb-2">
                                <span class="badge badge-@(positionSkill.IsRequired == true ? "danger" : "secondary") badge-pill">
                                    @positionSkill.Skill?.Name
                                    @if (positionSkill.IsRequired == true)
                                    {
                                        <i class="fas fa-star" title="Bắt buộc"></i>
                                    }
                                </span>
                            </div>
                        }
                    </div>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-star text-danger"></i> Kỹ năng bắt buộc |
                        <i class="fas fa-circle text-secondary"></i> Kỹ năng ưu tiên
                    </small>
                </div>
            </div>
        }

        <!-- Applications -->
        <div class="card card-success">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt"></i>
                    Đơn ứng tuyển (@applicationCount)
                </h3>
            </div>
            <div class="card-body">
                @if (Model.Applications != null && Model.Applications.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Ứng viên</th>
                                    <th>Email</th>
                                    <th>Ngày nộp</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var application in Model.Applications.OrderByDescending(a => a.AppliedAt).Take(10))
                                {
                                    <tr>
                                        <td>
                                            @if (application.User?.UserProfile != null)
                                            {
                                                <strong>@($"{application.User.UserProfile.FirstName} {application.User.UserProfile.LastName}")</strong>
                                            }
                                            else
                                            {
                                                <span class="text-muted">@application.User?.Email</span>
                                            }
                                        </td>
                                        <td>@application.User?.Email</td>
                                        <td>@application.AppliedAt?.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <span class="badge badge-primary">@application.CurrentStatus</span>
                                        </td>
                                        <td>
                                            <a href="@Url.Action("Details", "Applications", new { area = "Admin", id = application.ApplicationId })"
                                               class="btn btn-xs btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    @if (applicationCount > 10)
                    {
                        <div class="text-center">
                            <small class="text-muted">Hiển thị 10/@applicationCount đơn ứng tuyển gần nhất</small>
                            <br>
                            <a href="@Url.Action("Index", "Applications", new { area = "Admin", positionId = Model.PositionId })"
                               class="btn btn-sm btn-primary mt-2">
                                Xem tất cả đơn ứng tuyển
                            </a>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-3">
                        <i class="fas fa-file-alt fa-2x text-gray-300 mb-2"></i>
                        <p class="text-gray-500">Chưa có đơn ứng tuyển nào.</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Statistics -->
        <div class="card card-warning">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-bar"></i>
                    Thống kê
                </h3>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-right">
                            <h4 class="text-primary">@applicationCount</h4>
                            <small class="text-muted">Đơn ứng tuyển</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">@(Model.Applications?.Count(a => a.CurrentStatus == "approved") ?? 0)</h4>
                        <small class="text-muted">Đã duyệt</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-right">
                            <h4 class="text-info">@(Model.PositionSkills?.Count() ?? 0)</h4>
                            <small class="text-muted">Kỹ năng yêu cầu</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">@(Model.PositionSkills?.Count(s => s.IsRequired == true) ?? 0)</h4>
                        <small class="text-muted">Kỹ năng bắt buộc</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Creator Info -->
        <div class="card card-info">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user"></i>
                    Thông tin tạo
                </h3>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-circle fa-3x text-info"></i>
                    </div>
                    <h6>
                        @if (Model.CreatedByNavigation?.UserProfile != null)
                        {
                            @($"{Model.CreatedByNavigation.UserProfile.FirstName} {Model.CreatedByNavigation.UserProfile.LastName}")
                        }
                        else
                        {
                            @(Model.CreatedByNavigation?.Email ?? "N/A")
                        }
                    </h6>
                    <p class="text-muted">@Model.CreatedByNavigation?.Role?.RoleName</p>
                    <small class="text-muted">
                        Tạo lúc: @(Model.CreatedAt?.ToString("dd/MM/yyyy HH:mm") ?? "N/A")
                    </small>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card card-secondary">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i>
                    Thao tác
                </h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="@Url.Action("Index", "Positions", new { area = "Admin" })"
                       class="btn btn-secondary btn-block mb-2">
                        <i class="fas fa-arrow-left"></i>
                        Quay lại danh sách
                    </a>

                    @if (Model.IsActive == true)
                    {
                        <button type="button"
                                class="btn btn-warning btn-block mb-2"
                                onclick="showStatusModal(@Model.PositionId, '@Model.Title', true)">
                            <i class="fas fa-pause"></i>
                            Vô hiệu hóa vị trí
                        </button>
                    }
                    else
                    {
                        <button type="button"
                                class="btn btn-success btn-block mb-2"
                                onclick="showStatusModal(@Model.PositionId, '@Model.Title', false)">
                            <i class="fas fa-play"></i>
                            Kích hoạt vị trí
                        </button>
                    }

                    <button type="button"
                            class="btn btn-danger btn-block"
                            onclick="showDeleteModal(@Model.PositionId, '@Model.Title', '@(Model.Company?.Name ?? "N/A")')">
                        <i class="fas fa-trash"></i>
                        Xóa vị trí
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include the same modals from Index page -->
<!-- Modal thay đổi trạng thái -->
<div class="modal fade" id="statusModal" tabindex="-1" role="dialog" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form id="statusForm" method="post" action="@Url.Action("ToggleStatus", "Positions", new { area = "Admin" })">
                @Html.AntiForgeryToken()
                <input type="hidden" name="id" id="positionIdForStatus" />

                <div class="modal-header">
                    <h5 class="modal-title" id="statusModalLabel">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Xác nhận thay đổi trạng thái
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Đóng">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="statusConfirmText"></span>
                    </div>

                    <div class="form-group">
                        <label for="statusReason">
                            <i class="fas fa-clipboard-list"></i>
                            Lý do (tùy chọn):
                        </label>
                        <textarea class="form-control"
                                  name="reason"
                                  id="statusReason"
                                  rows="2"
                                  placeholder="Nhập lý do thay đổi trạng thái..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="adminPasswordStatus">
                            <i class="fas fa-key"></i>
                            Mật khẩu admin: <span class="text-danger">*</span>
                        </label>
                        <input type="password"
                               class="form-control"
                               name="adminPassword"
                               id="adminPasswordStatus"
                               placeholder="Nhập mật khẩu admin"
                               required />
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Hủy
                    </button>
                    <button type="submit" class="btn btn-primary" id="statusConfirmBtn">
                        <i class="fas fa-check"></i> Xác nhận
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal xác nhận xóa -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form id="deleteForm" method="post" action="@Url.Action("Delete", "Positions", new { area = "Admin" })">
                @Html.AntiForgeryToken()
                <input type="hidden" name="id" id="positionIdForDelete" />

                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-trash"></i>
                        Xác nhận xóa vị trí tuyển dụng
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Đóng">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Cảnh báo:</strong> <span id="deleteConfirmText"></span>
                        <br><small>Hành động này sẽ xóa tất cả đơn ứng tuyển liên quan!</small>
                    </div>

                    <div class="form-group">
                        <label for="deleteReason">
                            <i class="fas fa-clipboard-list"></i>
                            Lý do xóa: <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control"
                                  name="reason"
                                  id="deleteReason"
                                  rows="3"
                                  placeholder="Nhập lý do xóa vị trí (ví dụ: vi phạm chính sách, thông tin sai lệch...)"
                                  required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="adminPasswordDelete">
                            <i class="fas fa-key"></i>
                            Mật khẩu admin: <span class="text-danger">*</span>
                        </label>
                        <input type="password"
                               class="form-control"
                               name="adminPassword"
                               id="adminPasswordDelete"
                               placeholder="Nhập mật khẩu admin để xác nhận"
                               required />
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Hủy
                    </button>
                    <button type="submit" class="btn btn-danger" id="deleteConfirmBtn">
                        <i class="fas fa-trash"></i> Xóa vị trí
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showStatusModal(positionId, positionTitle, isActive) {
            console.log('showStatusModal called with:', { positionId, positionTitle, isActive });

            // Set position ID
            $('#positionIdForStatus').val(positionId);

            // Clear form fields
            $('#adminPasswordStatus').val('');
            $('#statusReason').val('');

            // Set confirmation text and button based on current status
            let confirmText, buttonText, buttonClass;
            if (isActive) {
                confirmText = `Bạn có chắc chắn muốn vô hiệu hóa vị trí "${positionTitle}" không?`;
                buttonText = '<i class="fas fa-pause"></i> Vô hiệu hóa';
                buttonClass = 'btn-warning';
                $('#statusModalLabel').html('<i class="fas fa-pause text-warning"></i> Xác nhận vô hiệu hóa vị trí');
            } else {
                confirmText = `Bạn có chắc chắn muốn kích hoạt vị trí "${positionTitle}" không?`;
                buttonText = '<i class="fas fa-play"></i> Kích hoạt';
                buttonClass = 'btn-success';
                $('#statusModalLabel').html('<i class="fas fa-play text-success"></i> Xác nhận kích hoạt vị trí');
            }

            $('#statusConfirmText').text(confirmText);
            $('#statusConfirmBtn').removeClass('btn-primary btn-success btn-warning').addClass(buttonClass).html(buttonText);

            // Show modal
            $('#statusModal').modal('show');
        }

        function showDeleteModal(positionId, positionTitle, companyName) {
            console.log('showDeleteModal called with:', { positionId, positionTitle, companyName });

            // Set position ID
            $('#positionIdForDelete').val(positionId);

            // Clear form fields
            $('#adminPasswordDelete').val('');
            $('#deleteReason').val('');

            // Set confirmation text
            const confirmText = `Bạn sắp xóa vĩnh viễn vị trí "${positionTitle}" của công ty "${companyName}".`;
            $('#deleteConfirmText').text(confirmText);

            // Show modal
            $('#deleteModal').modal('show');
        }

        $(document).ready(function() {
            // Handle status form submission
            $('#statusForm').on('submit', function(e) {
                const password = $('#adminPasswordStatus').val().trim();
                if (!password) {
                    e.preventDefault();
                    alert('Vui lòng nhập mật khẩu admin!');
                    $('#adminPasswordStatus').focus();
                    return false;
                }
            });

            // Handle delete form submission
            $('#deleteForm').on('submit', function(e) {
                const password = $('#adminPasswordDelete').val().trim();
                const reason = $('#deleteReason').val().trim();

                if (!password) {
                    e.preventDefault();
                    alert('Vui lòng nhập mật khẩu admin!');
                    $('#adminPasswordDelete').focus();
                    return false;
                }

                if (!reason) {
                    e.preventDefault();
                    alert('Vui lòng nhập lý do xóa vị trí!');
                    $('#deleteReason').focus();
                    return false;
                }

                // Final confirmation
                if (!confirm('Bạn có thực sự chắc chắn muốn xóa vị trí này không?\n\nViệc xóa sẽ:\n- Xóa vị trí tuyển dụng\n- Xóa tất cả đơn ứng tuyển\n- Xóa tất cả dữ liệu liên quan\n\nHành động này không thể hoàn tác!')) {
                    e.preventDefault();
                    return false;
                }
            });

            // Clear modals when hidden
            $('#statusModal').on('hidden.bs.modal', function () {
                $('#adminPasswordStatus').val('');
                $('#statusReason').val('');
                $('#positionIdForStatus').val('');
            });

            $('#deleteModal').on('hidden.bs.modal', function () {
                $('#adminPasswordDelete').val('');
                $('#deleteReason').val('');
                $('#positionIdForDelete').val('');
            });

            // Focus on password field when modals are shown
            $('#statusModal').on('shown.bs.modal', function () {
                $('#adminPasswordStatus').focus();
            });

            $('#deleteModal').on('shown.bs.modal', function () {
                $('#deleteReason').focus();
            });

            console.log('Position details scripts loaded successfully');
        });
    </script>
}