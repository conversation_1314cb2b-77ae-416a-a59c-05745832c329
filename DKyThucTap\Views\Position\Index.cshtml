@model DKyThucTap.Models.DTOs.Position.PositionSearchResultDto
@{
    ViewData["Title"] = "Danh sách vị trí tuyển dụng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase me-2"></i>
                        Danh sách vị trí tuyển dụng
                    </h3>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <div class="btn-group">
                            <a href="@Url.Action("My", "Position")" class="btn btn-outline-primary">
                                <i class="fas fa-user me-1"></i>
                                Vị trí của tôi
                            </a>
                            <a href="@Url.Action("Create", "Position")" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                Tạo vị trí mới
                            </a>
                        </div>
                    }
                </div>

                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Tìm kiếm theo tên vị trí, mô tả, công ty..." 
                                           value="@ViewBag.Search">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select name="pageSize" class="form-select" onchange="this.form.submit()">
                                    <option value="12" @@(ViewBag.PageSize == 12 ? "selected" : "")>12 / trang</option>
                                    <option value="24" @@(ViewBag.PageSize == 24 ? "selected" : "")>24 / trang</option>
                                    <option value="36" @@(ViewBag.PageSize == 36 ? "selected" : "")>36 / trang</option>
                                </select>
                            </div>
                        </div>
                    </form>

                    <!-- Results -->
                    @if (Model.Positions.Any())
                    {
                        <div class="row">
                            @foreach (var position in Model.Positions)
                            {
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 position-card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start mb-3">
                                                @if (!string.IsNullOrEmpty(position.CompanyLogoUrl))
                                                {
                                                    <img src="@position.CompanyLogoUrl" alt="@position.CompanyName" 
                                                         class="company-logo me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="company-logo-placeholder me-3">
                                                        <i class="fas fa-building"></i>
                                                    </div>
                                                }
                                                <div class="flex-grow-1">
                                                    <h5 class="card-title mb-1">
                                                        <a href="@Url.Action("Details", "Position", new { id = position.PositionId })" 
                                                           class="text-decoration-none">
                                                            @position.Title
                                                        </a>
                                                    </h5>
                                                    <p class="text-muted mb-0">@position.CompanyName</p>
                                                </div>
                                                <span class="badge @(position.IsActive == true ? "bg-success" : "bg-secondary")">
                                                    @(position.IsActive == true ? "Đang tuyển" : "Tạm dừng")
                                                </span>
                                            </div>

                                            <div class="mb-3">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                    <span class="text-muted">
                                                        @if (position.IsRemote == true)
                                                        {
                                                            <span class="badge bg-info me-1">Remote</span>
                                                        }
                                                        @(position.Location ?? "Không xác định")
                                                    </span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-briefcase text-muted me-2"></i>
                                                    <span class="text-muted">@position.PositionType</span>
                                                </div>
                                                @if (!string.IsNullOrEmpty(position.SalaryRange))
                                                {
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-dollar-sign text-muted me-2"></i>
                                                        <span class="text-muted">@position.SalaryRange</span>
                                                    </div>
                                                }
                                                @if (!string.IsNullOrEmpty(position.CategoryName))
                                                {
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-tag text-muted me-2"></i>
                                                        <span class="text-muted">@position.CategoryName</span>
                                                    </div>
                                                }
                                            </div>

                                            @if (position.RequiredSkills.Any())
                                            {
                                                <div class="mb-3">
                                                    @foreach (var skill in position.RequiredSkills.Take(3))
                                                    {
                                                        <span class="badge bg-light text-dark me-1">@skill</span>
                                                    }
                                                    @if (position.RequiredSkills.Count > 3)
                                                    {
                                                        <span class="text-muted">+@(position.RequiredSkills.Count - 3) khác</span>
                                                    }
                                                </div>
                                            }

                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-users me-1"></i>
                                                    @position.ApplicationCount ứng viên
                                                </small>
                                                @if (position.ApplicationDeadline.HasValue)
                                                {
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        Hạn: @position.ApplicationDeadline.Value.ToString("dd/MM/yyyy")
                                                    </small>
                                                }
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex justify-content-between">
                                                <small class="text-muted">
                                                    @position.CreatedAt?.ToString("dd/MM/yyyy")
                                                </small>
                                                <a href="@Url.Action("Details", "Position", new { id = position.PositionId })" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    Xem chi tiết
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <nav aria-label="Position pagination">
                                <ul class="pagination justify-content-center">
                                    @if (Model.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { page = Model.Page - 1, pageSize = ViewBag.PageSize, search = ViewBag.Search })">
                                                Trước
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.Page - 2); i <= Math.Min(Model.TotalPages, Model.Page + 2); i++)
                                    {
                                        <li class="page-item @(i == Model.Page ? "active" : "")">
                                            <a class="page-link" href="@Url.Action("Index", new { page = i, pageSize = ViewBag.PageSize, search = ViewBag.Search })">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    @if (Model.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("Index", new { page = Model.Page + 1, pageSize = ViewBag.PageSize, search = ViewBag.Search })">
                                                Sau
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Không tìm thấy vị trí nào</h4>
                            <p class="text-muted">Thử thay đổi từ khóa tìm kiếm hoặc tạo vị trí mới</p>
                            @if (User.Identity.IsAuthenticated)
                            {
                                <a href="@Url.Action("Create", "Position")" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo vị trí mới
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.position-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.position-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.company-logo {
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.company-logo-placeholder {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}
</style>
