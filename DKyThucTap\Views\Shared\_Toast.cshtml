﻿@* Views/Shared/_Toast.cshtml *@
@{
    var success = TempData["Success"] as string;
    var error = TempData["Error"] as string;
    var info = TempData["Info"] as string;
    var warning = TempData["Warning"] as string;
}

<link href="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.js"></script>

<script>
    // ✅ Cấu hình giao diện toast
    toastr.options = {
        "closeButton": true,
        "progressBar": true,
        "positionClass": "toast-top-right",     // ✅ Góc trên bên phải
        "timeOut": "4000",
        "extendedTimeOut": "1000",
        "showDuration": "300",
        "hideDuration": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut",
        "preventDuplicates": true
    };

    // ✅ Hiển thị toast tương ứng với TempData
    window.onload = function () {
        @if (!string.IsNullOrEmpty(success))
        {
                <text>toastr.success("@success");</text>
        }
        @if (!string.IsNullOrEmpty(error))
        {
                <text>toastr.error("@error");</text>
        }
        @if (!string.IsNullOrEmpty(info))
        {
                <text>toastr.info("@info");</text>
        }
        @if (!string.IsNullOrEmpty(warning))
        {
                <text>toastr.warning("@warning");</text>
        }
    };
</script>
