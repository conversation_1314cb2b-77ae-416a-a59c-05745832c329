@model List<DKyThucTap.Models.DTOs.Company.CompanyRecruiterListDto>
@{
    ViewData["Title"] = "Yêu cầu tham gia công ty của tôi";
}

<!-- Notification Container -->
<div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-hourglass-half me-2"></i>
                        Yêu cầu tham gia công ty của tôi
                    </h3>
                    <div class="btn-group">
                        <a href="@Url.Action("Index", "Company")" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>
                            T<PERSON><PERSON> công ty khác
                        </a>
                        <a href="@Url.Action("My", "Company")" class="btn btn-outline-secondary">
                            <i class="fas fa-building me-1"></i>
                            Công ty của tôi
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if (Model.Any())
                    {
                        <!-- Statistics -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4 class="mb-0">@Model.Count</h4>
                                        <p class="mb-0">Tổng yêu cầu</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h4 class="mb-0">@Model.Count(r => r.Status == "pending")</h4>
                                        <p class="mb-0">Chờ phê duyệt</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4 class="mb-0">@Model.Count(r => r.Status == "invited")</h4>
                                        <p class="mb-0">Được mời</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Requests Table -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Công ty</th>
                                        <th>Loại yêu cầu</th>
                                        <th>Trạng thái</th>
                                        <th>Lời nhắn</th>
                                        <th>Thời gian gửi</th>
                                        <th>Phản hồi</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var request in Model.OrderByDescending(r => r.LastActivity))
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(request.CompanyLogoUrl))
                                                    {
                                                        <img src="@request.CompanyLogoUrl" alt="@request.CompanyName" 
                                                             class="me-2" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                                    }
                                                    else
                                                    {
                                                        <div class="me-2" style="width: 40px; height: 40px; background-color: #f8f9fa; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                                            <i class="fas fa-building text-muted"></i>
                                                        </div>
                                                    }
                                                    <div>
                                                        <strong>
                                                            <a href="@Url.Action("Details", "Company", new { id = request.CompanyId })" 
                                                               class="text-decoration-none">
                                                                @request.CompanyName
                                                            </a>
                                                        </strong>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge @(request.Status == "invited" ? "bg-info" : "bg-secondary")">
                                                    @(request.Status == "invited" ? "Được mời" : "Tự yêu cầu")
                                                </span>
                                            </td>
                                            <td>
                                                @switch (request.Status?.ToLower())
                                                {
                                                    case "pending":
                                                        <span class="badge bg-warning">Chờ phê duyệt</span>
                                                        break;
                                                    case "invited":
                                                        <span class="badge bg-info">Được mời</span>
                                                        break;
                                                    case "approved":
                                                        <span class="badge bg-success">Đã phê duyệt</span>
                                                        break;
                                                    case "rejected":
                                                        <span class="badge bg-danger">Đã từ chối</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@request.Status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(request.RequestMessage))
                                                {
                                                    <span class="text-truncate" style="max-width: 200px;" title="@request.RequestMessage">
                                                        @request.RequestMessage
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Không có lời nhắn</span>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    @request.LastActivity?.ToString("dd/MM/yyyy HH:mm")
                                                </small>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(request.ResponseMessage))
                                                {
                                                    <span class="text-truncate" style="max-width: 150px;" title="@request.ResponseMessage">
                                                        @request.ResponseMessage
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa có phản hồi</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="@Url.Action("Details", "Company", new { id = request.CompanyId })"
                                                       class="btn btn-outline-info" title="Xem công ty">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (request.Status == "invited")
                                                    {
                                                        <button type="button" class="btn btn-success"
                                                                onclick="respondToInvitation(@request.CompanyId, true, '@request.CompanyName')"
                                                                title="Chấp nhận lời mời">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-danger"
                                                                onclick="respondToInvitation(@request.CompanyId, false, '@request.CompanyName')"
                                                                title="Từ chối lời mời">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    }
                                                    else if (request.Status == "pending")
                                                    {
                                                        <button type="button" class="btn btn-outline-danger"
                                                                onclick="cancelRequest(@request.CompanyId, '@request.CompanyName')"
                                                                title="Hủy yêu cầu">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-hourglass-half fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Bạn chưa có yêu cầu tham gia công ty nào</h4>
                            <p class="text-muted">Tìm kiếm và yêu cầu tham gia các công ty mà bạn quan tâm</p>
                            <div class="mt-3">
                                <a href="@Url.Action("Index", "Company")" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>
                                    Tìm công ty
                                </a>
                                <a href="@Url.Action("Create", "Company")" class="btn btn-outline-success">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo công ty mới
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
@@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-notification {
    min-width: 300px;
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

.toast-notification:hover {
    transform: translateX(-5px);
}

#notificationContainer {
    max-width: 400px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>

@section Scripts {
<script>
// Respond to invitation function
function respondToInvitation(companyId, accept, companyName) {
    const action = accept ? 'chấp nhận' : 'từ chối';
    const message = accept ?
        `Bạn có chắc chắn muốn chấp nhận lời mời từ công ty "${companyName}"?` :
        `Bạn có chắc chắn muốn từ chối lời mời từ công ty "${companyName}"?`;

    if (confirm(message)) {
        const formData = new FormData();
        formData.append('companyId', companyId);
        formData.append('accept', accept);
        formData.append('responseMessage', '');
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("RespondToInvitation", "Company")', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', data.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', `Có lỗi xảy ra khi ${action} lời mời`);
        });
    }
}

// Cancel request function
function cancelRequest(companyId, companyName) {
    if (confirm(`Bạn có chắc chắn muốn hủy yêu cầu tham gia công ty "${companyName}"?`)) {
        const formData = new FormData();
        formData.append('companyId', companyId);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("Leave", "Company")', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('success', 'Đã hủy yêu cầu thành công');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Có lỗi xảy ra khi hủy yêu cầu');
        });
    }
}

// Notification system
function showNotification(type, message) {
    const container = document.getElementById('notificationContainer');
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show toast-notification`;
    
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to container
    container.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}
</script>
}

<!-- Add CSRF Token -->
@Html.AntiForgeryToken()
