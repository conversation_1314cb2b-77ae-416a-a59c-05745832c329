@model List<DKyThucTap.Models.DTOs.Company.CompanyListDto>
@{
    ViewData["Title"] = "Danh sách công ty";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-building me-2"></i>
                        Danh sách công ty
                    </h3>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <div class="btn-group">
                            <a href="@Url.Action("My", "Company")" class="btn btn-outline-primary">
                                <i class="fas fa-user me-1"></i>
                                Công ty của tôi
                            </a>
                            @if (User.HasClaim("Permission", "create_company"))
                            {
                                <a href="@Url.Action("Create", "Company")" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo công ty mới
                                </a>
                            }
                        </div>
                    }
                </div>

                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-10">
                                <div class="input-group">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Tìm kiếm theo tên công ty, ngành nghề, địa điểm..." 
                                           value="@ViewBag.Search">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                @if (!string.IsNullOrEmpty(ViewBag.Search as string))
                                {
                                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-times me-1"></i>Xóa bộ lọc
                                    </a>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- Results -->
                    @if (Model.Any())
                    {
                        <div class="row">
                            @foreach (var company in Model)
                            {
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 company-card">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start mb-3">
                                                @if (!string.IsNullOrEmpty(company.LogoUrl))
                                                {
                                                    <img src="@company.LogoUrl" alt="@company.Name" 
                                                         class="company-logo me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="company-logo-placeholder me-3">
                                                        <i class="fas fa-building"></i>
                                                    </div>
                                                }
                                                <div class="flex-grow-1">
                                                    <h5 class="card-title mb-1">
                                                        <a href="@Url.Action("Details", "Company", new { id = company.CompanyId })" 
                                                           class="text-decoration-none">
                                                            @company.Name
                                                        </a>
                                                    </h5>
                                                    @if (!string.IsNullOrEmpty(company.Industry))
                                                    {
                                                        <p class="text-muted mb-0">@company.Industry</p>
                                                    }
                                                </div>
                                                @if (!string.IsNullOrEmpty(company.UserRole))
                                                {
                                                    <span class="badge @(company.UserRole == "Owner" ? "bg-success" : company.UserRole == "Recruiter" ? "bg-primary" : "bg-warning")">
                                                        @(company.UserRole == "Owner" ? "Chủ sở hữu" : company.UserRole == "Recruiter" ? "Nhân viên" : "Chờ duyệt")
                                                    </span>
                                                }
                                            </div>

                                            @if (!string.IsNullOrEmpty(company.Description))
                                            {
                                                <p class="text-muted mb-3">
                                                    @(company.Description.Length > 100 ? company.Description.Substring(0, 100) + "..." : company.Description)
                                                </p>
                                            }

                                            <div class="mb-3">
                                                @if (!string.IsNullOrEmpty(company.Location))
                                                {
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                                        <span class="text-muted">@company.Location</span>
                                                    </div>
                                                }
                                                @if (!string.IsNullOrEmpty(company.Website))
                                                {
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="fas fa-globe text-muted me-2"></i>
                                                        <a href="@company.Website" target="_blank" class="text-decoration-none">
                                                            Website
                                                        </a>
                                                    </div>
                                                }
                                            </div>

                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-primary mb-0">@company.PositionCount</h6>
                                                        <small class="text-muted">Vị trí</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="border-end">
                                                        <h6 class="text-success mb-0">@company.ActivePositionCount</h6>
                                                        <small class="text-muted">Đang tuyển</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <h6 class="text-info mb-0">@company.RecruiterCount</h6>
                                                    <small class="text-muted">Nhân viên</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    @company.CreatedAt?.ToString("dd/MM/yyyy")
                                                </small>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="@Url.Action("Details", "Company", new { id = company.CompanyId })"
                                                       class="btn btn-outline-primary">
                                                        Xem chi tiết
                                                    </a>
                                                    @if (User.Identity.IsAuthenticated && string.IsNullOrEmpty(company.UserRole))
                                                    {
                                                        <button type="button" class="btn btn-success"
                                                                onclick="quickJoinRequest(@company.CompanyId, '@company.Name')">
                                                            <i class="fas fa-user-plus"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">
                                @if (!string.IsNullOrEmpty(ViewBag.Search as string))
                                {
                                    <span>Không tìm thấy công ty nào</span>
                                }
                                else
                                {
                                    <span>Chưa có công ty nào</span>
                                }
                            </h4>
                            <p class="text-muted">
                                @if (!string.IsNullOrEmpty(ViewBag.Search as string))
                                {
                                    <span>Thử thay đổi từ khóa tìm kiếm</span>
                                }
                                else
                                {
                                    <span>Hãy là người đầu tiên tạo công ty</span>
                                }
                            </p>
                            @if (User.Identity.IsAuthenticated && User.HasClaim("Permission", "create_company"))
                            {
                                <a href="@Url.Action("Create", "Company")" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo công ty mới
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.company-card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.company-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.company-logo {
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.company-logo-placeholder {
    width: 60px;
    height: 60px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 24px;
}

@@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-notification {
    min-width: 300px;
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
}

.toast-notification:hover {
    transform: translateX(-5px);
}

#notificationContainer {
    max-width: 400px;
}
</style>

<!-- Notification Container -->
<div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

<!-- Quick Join Modal -->
<div class="modal fade" id="quickJoinModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    Yêu cầu tham gia công ty
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickJoinForm">
                <div class="modal-body">
                    <input type="hidden" id="quickJoinCompanyId" name="CompanyId" />

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Bạn đang yêu cầu tham gia công ty <strong id="quickJoinCompanyName"></strong>.
                    </div>

                    <div class="mb-3">
                        <label for="quickJoinMessage" class="form-label">Lời nhắn (tùy chọn)</label>
                        <textarea class="form-control" id="quickJoinMessage" name="Message" rows="3"
                                  placeholder="Giới thiệu ngắn gọn về bản thân..."></textarea>
                        <div class="form-text">Tối đa 500 ký tự</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>
                        Gửi yêu cầu
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quick Join Form
    const quickJoinForm = document.getElementById('quickJoinForm');
    if (quickJoinForm) {
        quickJoinForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang gửi...';

            // Prepare form data
            const formData = new FormData(this);

            // Add CSRF token to form data
            const csrfToken = document.querySelector('input[name="__RequestVerificationToken"]').value;
            formData.append('__RequestVerificationToken', csrfToken);

            // Submit via AJAX
            fetch('@Url.Action("RequestToJoin", "Company")', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('success', data.message);
                    bootstrap.Modal.getInstance(document.getElementById('quickJoinModal')).hide();
                    this.reset();
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('error', data.message);
                    if (data.errors && data.errors.length > 0) {
                        data.errors.forEach(error => showNotification('error', error));
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Có lỗi xảy ra khi gửi yêu cầu');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

// Quick join request function
function quickJoinRequest(companyId, companyName) {
    document.getElementById('quickJoinCompanyId').value = companyId;
    document.getElementById('quickJoinCompanyName').textContent = companyName;
    new bootstrap.Modal(document.getElementById('quickJoinModal')).show();
}

// Notification system
function showNotification(type, message) {
    const container = document.getElementById('notificationContainer');

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show toast-notification`;

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to container
    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}
</script>
}

<!-- Add CSRF Token -->
@Html.AntiForgeryToken()
