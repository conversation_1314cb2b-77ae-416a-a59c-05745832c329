-- Script to create test users for debugging authentication
-- Run this after the application has initialized the default roles

-- First, check if roles exist
SELECT * FROM roles;

-- Create test users (passwords are hashed for "Test123")
-- Note: You should run the application first to get the actual BCrypt hashes

-- Test Candidate User
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    DECLARE @CandidateRoleId INT = (SELECT role_id FROM roles WHERE role_name = 'Candidate');
    
    INSERT INTO users (email, password_hash, role_id, created_at, is_active)
    VALUES ('<EMAIL>', '$2a$10$placeholder_hash_here', @CandidateRoleId, GETUTCDATE(), 1);
    
    DECLARE @CandidateUserId INT = SCOPE_IDENTITY();
    
    INSERT INTO user_profiles (user_id, first_name, last_name, updated_at)
    VALUES (@CandidateUserId, 'Test', 'Candidate', GETUTCDATE());
END

-- Test Recruiter User
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    DECLARE @RecruiterRoleId INT = (SELECT role_id FROM roles WHERE role_name = 'Recruiter');
    
    INSERT INTO users (email, password_hash, role_id, created_at, is_active)
    VALUES ('<EMAIL>', '$2a$10$placeholder_hash_here', @RecruiterRoleId, GETUTCDATE(), 1);
    
    DECLARE @RecruiterUserId INT = SCOPE_IDENTITY();
    
    INSERT INTO user_profiles (user_id, first_name, last_name, updated_at)
    VALUES (@RecruiterUserId, 'Test', 'Recruiter', GETUTCDATE());
END

-- Test Admin User
IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
BEGIN
    DECLARE @AdminRoleId INT = (SELECT role_id FROM roles WHERE role_name = 'Admin');
    
    INSERT INTO users (email, password_hash, role_id, created_at, is_active)
    VALUES ('<EMAIL>', '$2a$10$placeholder_hash_here', @AdminRoleId, GETUTCDATE(), 1);
    
    DECLARE @AdminUserId INT = SCOPE_IDENTITY();
    
    INSERT INTO user_profiles (user_id, first_name, last_name, updated_at)
    VALUES (@AdminUserId, 'Test', 'Admin', GETUTCDATE());
END

-- Verify created users
SELECT 
    u.user_id,
    u.email,
    u.is_active,
    r.role_name,
    up.first_name,
    up.last_name,
    u.created_at
FROM users u
INNER JOIN roles r ON u.role_id = r.role_id
LEFT JOIN user_profiles up ON u.user_id = up.user_id
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
