﻿@model IEnumerable<DKyThucTap.Models.Conversation>
@{
    ViewData["Title"] = "Tin nhắn realtime";
    var currentUserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
}

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.0/signalr.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />

<div class="container py-4">
    <div class="row">
        <!-- Sidebar danh sách hội thoại -->
        <div class="col-md-4">
            <div class="list-group shadow-sm" id="conversationList">
                @if (!Model.Any())
                {
                    <div class="list-group-item text-center text-muted">
                        <i class="fas fa-comments"></i> Chưa có cuộc trò chuyện nào
                    </div>
                }
                else
                {
                    @foreach (var conv in Model)
                    {
                        var otherUser = conv.Participant1UserId.ToString() == currentUserId
                        ? conv.Participant2User
                        : conv.Participant1User;

                        var isRecruiter = otherUser.Role?.RoleName == "recruiter";
                        var profileUrl = isRecruiter
                        ? Url.Action("Recruiter", "Profile", new { recruiterId = otherUser.UserId })
                        : Url.Action("Candidate", "Profile", new { id = otherUser.UserId });

                        <a href="javascript:void(0)"
                           class="list-group-item list-group-item-action conversation-item"
                           data-conversation-id="@conv.ConversationId"
                           data-other-id="@(otherUser.UserId)"
                           data-name="@($"{otherUser.UserProfile?.FirstName} {otherUser.UserProfile?.LastName}")"
                           data-avatar="@((otherUser.UserProfile?.ProfilePictureUrl) ?? "/images/profiles/default-avatar.png")"
                           data-profile-url="@profileUrl"
                           id="<EMAIL>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <img src="@((otherUser.UserProfile?.ProfilePictureUrl) ?? "/images/profiles/default-avatar.png")"
                                         class="rounded-circle me-2" width="40" height="40" />
                                    <span>@($"{otherUser.UserProfile?.FirstName} {otherUser.UserProfile?.LastName}")</span>
                                    <small class="text-muted d-block">
                                        Cập nhật: @conv.LastMessageAt?.ToLocalTime().ToString("HH:mm dd/MM")
                                    </small>
                                </div>
                                <span class="badge bg-danger d-none" id="<EMAIL>">0</span>
                            </div>
                        </a>
                    }
                }
            </div>
        </div>

        <!-- Cửa sổ chat -->
        <div class="col-md-8">
            <div class="card shadow-sm" id="chatCard" style="display:none;">
                <div class="card-header bg-primary text-white d-flex align-items-center" style="cursor:pointer;" id="chatHeader">
                    <img id="chatAvatar" src="/images/profiles/default-avatar.png" class="rounded-circle me-2" width="40" height="40" />
                    <strong id="chatName">Đang tải...</strong>
                </div>
                <div class="card-body" id="messagesList" style="height: 400px; overflow-y: auto; background-color:#f8f9fa;">
                    <!-- Tin nhắn sẽ hiển thị tại đây -->
                </div>
                <div class="card-footer">
                    <div class="input-group">
                        <input type="text" id="messageInput" class="form-control" placeholder="Nhập tin nhắn..." />
                        <button class="btn btn-success" id="sendButton">
                            <i class="fas fa-paper-plane"></i> Gửi
                        </button>
                    </div>
                </div>
            </div>
            <div class="alert alert-info text-center" id="noChat" style="display:block;">
                Chọn một cuộc trò chuyện để bắt đầu.
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const currentUserId = "@currentUserId";
        let selectedConversationId = null;
        let receiverUserId = null;
        let connectionStarted = false;
        let profileUrl = null;

        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/chathub")
            .build();

        async function startConnection() {
            try {
                await connection.start();
                connectionStarted = true;
                console.log("✅ Connected to ChatHub");
            } catch (err) {
                console.error("❌ Lỗi kết nối SignalR:", err);
                setTimeout(startConnection, 3000);
            }
        }
        startConnection();

        // Nhận tin nhắn realtime
        connection.on("ReceiveMessage", function (senderId, message, time) {
            if (selectedConversationId === null) return;

            const alignment = senderId == currentUserId ? "justify-content-end" : "justify-content-start";
            const bubbleClass = senderId == currentUserId ? "bg-success text-white" : "bg-light";
            const avatarHtml = senderId == currentUserId ? "" :
                `<img src="${document.getElementById("chatAvatar").src}"
                      class="rounded-circle me-2" width="30" height="30" />`;

            const msgHtml = `
                <div class="d-flex ${alignment} mb-2">
                    ${avatarHtml}
                    <div class="p-2 rounded ${bubbleClass}" style="max-width:70%;">
                        ${message}
                        <div class="small text-muted">${time}</div>
                    </div>
                </div>`;
            document.getElementById("messagesList").innerHTML += msgHtml;
            scrollToBottom();
        });

        // Hàm gửi tin nhắn
        async function sendMessage() {
            const messageInput = document.getElementById("messageInput");
            const message = messageInput.value.trim();

            if (!message || !selectedConversationId) return;

            if (!connectionStarted) {
                console.warn("⚠️ Connection chưa sẵn sàng!");
                return;
            }

            try {
                await connection.invoke("SendMessage", parseInt(selectedConversationId), message);
                messageInput.value = "";
            } catch (err) {
                console.error("❌ Lỗi khi gửi tin nhắn:", err);
            }
        }

        document.getElementById("sendButton").addEventListener("click", sendMessage);

        document.getElementById("messageInput").addEventListener("keypress", function (e) {
            if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Chọn hội thoại
        document.querySelectorAll(".conversation-item").forEach(item => {
            item.addEventListener("click", () => {
                selectedConversationId = item.getAttribute("data-conversation-id");
                receiverUserId = item.getAttribute("data-other-id");
                const name = item.getAttribute("data-name");
                const avatar = item.getAttribute("data-avatar");
                profileUrl = item.getAttribute("data-profile-url");

                document.getElementById("chatCard").style.display = "block";
                document.getElementById("noChat").style.display = "none";
                document.getElementById("chatName").innerText = name;
                document.getElementById("chatAvatar").src = avatar;

                // Gắn sự kiện click cho header
                document.getElementById("chatHeader").onclick = () => {
                    if (profileUrl) {
                        window.location.href = profileUrl;
                    }
                };

                // Load lịch sử tin nhắn
                fetch(`/Messages/GetMessages?conversationId=${selectedConversationId}`)
                    .then(res => res.json())
                    .then(messages => {
                        const messagesList = document.getElementById("messagesList");
                        messagesList.innerHTML = "";
                        messages.forEach(m => {
                            const alignment = m.senderUserId == currentUserId ? "justify-content-end" : "justify-content-start";
                            const bubbleClass = m.senderUserId == currentUserId ? "bg-success text-white" : "bg-light";
                            const avatarHtml = m.senderUserId == currentUserId ? "" :
                                `<img src="${avatar}" class="rounded-circle me-2" width="30" height="30" />`;

                            const msgHtml = `
                                <div class="d-flex ${alignment} mb-2">
                                    ${avatarHtml}
                                    <div class="p-2 rounded ${bubbleClass}" style="max-width:70%;">
                                        ${m.messageText}
                                        <div class="small text-muted">${m.sentAt}</div>
                                    </div>
                                </div>`;
                            messagesList.innerHTML += msgHtml;
                        });
                        scrollToBottom();
                    });
            });
        });

                async function updateUnreadConversations() {
            try {
                const response = await fetch('/Messages/UnreadPerConversation');
                if (response.ok) {
                    const data = await response.json();

                    // Reset tất cả badge
                    document.querySelectorAll('[id^="badge-"]').forEach(badge => {
                        badge.textContent = 0;
                        badge.classList.add("d-none");
                        badge.closest("a").classList.remove("active", "bg-light");
                    });

                    // Cập nhật badge theo dữ liệu từ API
                    data.forEach(item => {
                        const badge = document.getElementById(`badge-${item.conversationId}`);
                        if (badge) {
                            badge.textContent = item.count;
                            badge.classList.remove("d-none");
                            const conversationItem = badge.closest("a");
                            conversationItem.classList.add("bg-light"); // highlight
                        }
                    });
                }
            } catch (error) {
                console.error("Lỗi khi cập nhật unread per conversation:", error);
            }
        }

        // Gọi lần đầu
        updateUnreadConversations();
        // Sau đó mỗi 3s gọi lại
        setInterval(updateUnreadConversations, 3000);


        function scrollToBottom() {
            const chatBox = document.getElementById("messagesList");
            chatBox.scrollTop = chatBox.scrollHeight;
        }
    </script>
}
