﻿using DKyThucTap.Data;
using DKyThucTap.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace DKyThucTap.Controllers
{
    [Authorize(Roles = "Candidate")] // chỉ ứng viên mới được ứng tuyển
    public class ApplicationsController : Controller
    {
        private readonly DKyThucTapContext _context;

        public ApplicationsController(DKyThucTapContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<IActionResult> Apply(int positionId)
        {
            var position = await _context.Positions
                .Include(p => p.Company)
                .Include(p => p.PositionSkills).ThenInclude(ps => ps.Skill)
                .FirstOrDefaultAsync(p => p.PositionId == positionId);

            if (position == null)
            {
                TempData["Error"] = "Không tìm thấy công việc này.";
                return RedirectToAction("Index", "Positions");
            }

            return View(position);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Apply(int positionId, string coverLetter, IFormFile? cvFile)
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                TempData["Error"] = "Bạn cần đăng nhập để ứng tuyển.";
                return RedirectToAction("Login", "Auth");
            }

            int userId = int.Parse(userIdClaim.Value);

            if (await _context.Applications.AnyAsync(a => a.PositionId == positionId && a.UserId == userId))
            {
                TempData["Warning"] = "Bạn đã ứng tuyển công việc này.";
                return RedirectToAction("Details", "Position", new { id = positionId });
            }

            string? cvPath = null;
            if (cvFile != null && cvFile.Length > 0)
            {
                var uploads = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/document/application");
                if (!Directory.Exists(uploads))
                {
                    Directory.CreateDirectory(uploads);
                }

                var extension = Path.GetExtension(cvFile.FileName);
                var fileName = $"cv-{userId}{extension}";
                var filePath = Path.Combine(uploads, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await cvFile.CopyToAsync(stream);
                }

                cvPath = $"/document/application/{fileName}";
            }

            var application = new Application
            {
                PositionId = positionId,
                UserId = userId,
                CoverLetter = coverLetter,
                AdditionalInfo = cvPath,
                CurrentStatus = "applied"
            };

            _context.Applications.Add(application);
            await _context.SaveChangesAsync();

            TempData["Success"] = "Ứng tuyển thành công!";
            return RedirectToAction("Details", "Position", new { id = positionId });
        }
    }
}
