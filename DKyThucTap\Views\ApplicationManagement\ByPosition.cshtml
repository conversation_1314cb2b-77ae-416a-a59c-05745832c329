@model DKyThucTap.Models.ViewModels.ApplicationManagementViewModel
@{
    ViewData["Title"] = $"Quản lý ứng viên - {Model.Position.Title}";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>Quản lý ứng viên</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Trang chủ</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("My", "Position")">Vị trí của tôi</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Details", "Position", new { id = Model.Position.PositionId })">@Model.Position.Title</a></li>
                            <li class="breadcrumb-item active">Quản lý ứng viên</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="@Url.Action("Details", "Position", new { id = Model.Position.PositionId })" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Quay lại vị trí
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">@Model.Position.Title</h4>
                            <p class="text-muted mb-0">
                                <i class="fas fa-building me-1"></i>@Model.Position.CompanyName
                                @if (!string.IsNullOrEmpty(Model.Position.Location))
                                {
                                    <span class="ms-3"><i class="fas fa-map-marker-alt me-1"></i>@Model.Position.Location</span>
                                }
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge @(Model.Position.IsActive == true ? "bg-success" : "bg-warning") fs-6">
                                @(Model.Position.IsActive == true ? "Đang tuyển" : "Tạm dừng")
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">@Model.Statistics.TotalApplications</h3>
                    <p class="mb-0">Tổng ứng viên</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">@Model.Statistics.PendingApplications</h3>
                    <p class="mb-0">Chờ xử lý</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">@Model.Statistics.ReviewingApplications</h3>
                    <p class="mb-0">Đang xem xét</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">@Model.Statistics.AcceptedApplications</h3>
                    <p class="mb-0">Đã chấp nhận</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Trạng thái</label>
                            <select name="status" class="form-select">
                                <option value="">Tất cả trạng thái</option>
                                <!option value="applied" @(Model.SearchCriteria.Status == "applied" ? "selected" : "")>Đã ứng tuyển</!option>
                                <!option value="reviewing" @(Model.SearchCriteria.Status == "reviewing" ? "selected" : "")>Đang xem xét</!option>
                                <!option value="interviewed" @(Model.SearchCriteria.Status == "interviewed" ? "selected" : "")>Đã phỏng vấn</!option>
                                <!option value="accepted" @(Model.SearchCriteria.Status == "accepted" ? "selected" : "")>Đã chấp nhận</!option>
                                <!option value="rejected" @(Model.SearchCriteria.Status == "rejected" ? "selected" : "")>Đã từ chối</!option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Tìm kiếm</label>
                            <input type="text" name="search" class="form-control" placeholder="Tên ứng viên, email..." 
                                   value="@Model.SearchCriteria.SearchTerm">
                        </div>
                        <select name="pageSize" class="form-select">
                            <!option value="10" @(Model.PageSize == 10 ? "selected" : "")>10</!option>
                            <!option value="20" @(Model.PageSize == 20 ? "selected" : "")>20</!option>
                            <!option value="50" @(Model.PageSize == 50 ? "selected" : "")>50</!option>
                        </select>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Tìm kiếm
                                </button>
                                <a href="@Url.Action("ByPosition", new { positionId = Model.Position.PositionId })" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Xóa bộ lọc
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Danh sách ứng viên (@Model.TotalCount)</h5>
                    @if (Model.Applications.Any())
                    {
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                <i class="fas fa-check-square me-1"></i>Chọn tất cả
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
                                <i class="fas fa-square me-1"></i>Bỏ chọn
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-success" onclick="bulkAction('reviewing')">
                                <i class="fas fa-eye me-1"></i>Đánh dấu xem xét
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="bulkAction('rejected')">
                                <i class="fas fa-times me-1"></i>Từ chối hàng loạt
                            </button>
                        </div>
                    }
                </div>
                <div class="card-body p-0">
                    @if (Model.Applications.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                        </th>
                                        <th>Ứng viên</th>
                                        <th>Liên hệ</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày ứng tuyển</th>
                                        <th>Ghi chú</th>
                                        <th width="150">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var application in Model.Applications)
                                    {
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="application-checkbox" value="@application.ApplicationId">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(application.ApplicantProfilePictureUrl))
                                                    {
                                                        <img src="@application.ApplicantProfilePictureUrl" alt="@application.ApplicantName" 
                                                             class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    }
                                                    else
                                                    {
                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    }
                                                    <div>
                                                        <strong>@application.ApplicantName</strong>
                                                        <div class="small text-muted">
                                                            @if (application.HasCv)
                                                            {
                                                                <i class="fas fa-file-pdf text-danger me-1" title="Có CV"></i>
                                                            }
                                                            @if (application.HasCoverLetter)
                                                            {
                                                                <i class="fas fa-envelope text-info me-1" title="Có thư xin việc"></i>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <i class="fas fa-envelope me-1"></i>@application.ApplicantEmail
                                                </div>
                                                @if (!string.IsNullOrEmpty(application.ApplicantPhone))
                                                {
                                                    <div class="small text-muted">
                                                        <i class="fas fa-phone me-1"></i>@application.ApplicantPhone
                                                    </div>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @GetStatusBadgeClass(application.CurrentStatus)">
                                                    @GetStatusDisplayName(application.CurrentStatus)
                                                </span>
                                            </td>
                                            <td>
                                                @application.AppliedAt?.ToString("dd/MM/yyyy HH:mm")
                                            </td>
                                            <td>
                                                @if (application.NotesCount > 0)
                                                {
                                                    <span class="badge bg-info">@application.NotesCount ghi chú</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Chưa có</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="@Url.Action("Details", new { id = application.ApplicationId })" 
                                                       class="btn btn-outline-primary" title="Xem chi tiết">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-success" 
                                                            onclick="quickStatusUpdate(@application.ApplicationId, 'reviewing')" 
                                                            title="Đánh dấu xem xét">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="quickStatusUpdate(@application.ApplicationId, 'rejected')" 
                                                            title="Từ chối">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (Model.TotalPages > 1)
                        {
                            <div class="card-footer">
                                <nav aria-label="Application pagination">
                                    <ul class="pagination justify-content-center mb-0">
                                        @if (Model.CurrentPage > 1)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("ByPosition", new { positionId = Model.Position.PositionId, page = Model.CurrentPage - 1, pageSize = Model.PageSize, status = Model.SearchCriteria.Status })">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        }

                                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                        {
                                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                <a class="page-link" href="@Url.Action("ByPosition", new { positionId = Model.Position.PositionId, page = i, pageSize = Model.PageSize, status = Model.SearchCriteria.Status })">@i</a>
                                            </li>
                                        }

                                        @if (Model.CurrentPage < Model.TotalPages)
                                        {
                                            <li class="page-item">
                                                <a class="page-link" href="@Url.Action("ByPosition", new { positionId = Model.Position.PositionId, page = Model.CurrentPage + 1, pageSize = Model.PageSize, status = Model.SearchCriteria.Status })">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </nav>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có ứng viên nào</h5>
                            <p class="text-muted">Vị trí này chưa nhận được đơn ứng tuyển nào.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "applied" => "bg-primary",
            "reviewing" => "bg-warning",
            "interviewed" => "bg-info",
            "accepted" => "bg-success",
            "rejected" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(string status)
    {
        return status switch
        {
            "applied" => "Đã ứng tuyển",
            "reviewing" => "Đang xem xét",
            "interviewed" => "Đã phỏng vấn",
            "accepted" => "Đã chấp nhận",
            "rejected" => "Đã từ chối",
            _ => status
        };
    }
}

<script>
// Selection management
function selectAll() {
    document.querySelectorAll('.application-checkbox').forEach(cb => cb.checked = true);
    updateSelectAllCheckbox();
}

function clearSelection() {
    document.querySelectorAll('.application-checkbox').forEach(cb => cb.checked = false);
    updateSelectAllCheckbox();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    document.querySelectorAll('.application-checkbox').forEach(cb => cb.checked = selectAllCheckbox.checked);
}

function updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll('.application-checkbox');
    const checkedBoxes = document.querySelectorAll('.application-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    
    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === checkboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Listen for checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('application-checkbox')) {
        updateSelectAllCheckbox();
    }
});

// Quick status update
function quickStatusUpdate(applicationId, newStatus) {
    if (confirm(`Bạn có chắc chắn muốn thay đổi trạng thái ứng viên này?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '@Url.Action("UpdateStatus")';
        
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            form.appendChild(token.cloneNode());
        }
        
        const applicationIdInput = document.createElement('input');
        applicationIdInput.type = 'hidden';
        applicationIdInput.name = 'ApplicationId';
        applicationIdInput.value = applicationId;
        form.appendChild(applicationIdInput);
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'NewStatus';
        statusInput.value = newStatus;
        form.appendChild(statusInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk actions
function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.application-checkbox:checked')).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Vui lòng chọn ít nhất một ứng viên');
        return;
    }
    
    if (confirm(`Bạn có chắc chắn muốn thực hiện hành động này cho ${selectedIds.length} ứng viên?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '@Url.Action("BulkUpdateStatus")';
        
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            form.appendChild(token.cloneNode());
        }
        
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'SelectedApplicationIds';
            input.value = id;
            form.appendChild(input);
        });
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'NewStatus';
        statusInput.value = action;
        form.appendChild(statusInput);
        
        const positionIdInput = document.createElement('input');
        positionIdInput.type = 'hidden';
        positionIdInput.name = 'PositionId';
        positionIdInput.value = '@Model.Position.PositionId';
        form.appendChild(positionIdInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

@Html.AntiForgeryToken()
