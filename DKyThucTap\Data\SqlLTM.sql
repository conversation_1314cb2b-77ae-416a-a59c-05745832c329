﻿Create database DKyThucTap
go

use D<PERSON>yThucTap
go

---

-- 1. Bảng `roles`: <PERSON><PERSON><PERSON> trữ các vai trò người dùng (ví dụ: '<PERSON>ng viên', 'nhà tuyển dụng', 'quản trị')
CREATE TABLE roles (
    role_id INT PRIMARY KEY IDENTITY(1,1),
    role_name VARCHAR(50) UNIQUE NOT NULL,
    permissions NVARCHAR(MAX) -- Dùng NVARCHAR(MAX) để lưu JSON cho quyền hạn
);

---

-- 2. Bảng `users`: Thông tin người dùng cơ bản
CREATE TABLE users (
    user_id INT PRIMARY KEY IDENTITY(1,1),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role_id INT NOT NULL REFERENCES roles(role_id),
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    last_login DATETIMEOFFSET,
    is_active BIT DEFAULT 1
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role_id);

---

-- 3. Bảng `user_profiles`: Thông tin hồ sơ chi tiết của người dùng
CREATE TABLE user_profiles (
    profile_id INT PRIMARY KEY IDENTITY(1,1),
    user_id INT UNIQUE NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address NVARCHAR(MAX),
    cv_url VARCHAR(255),
    profile_picture_url VARCHAR(255),
    bio NVARCHAR(MAX),
    updated_at DATETIMEOFFSET DEFAULT GETUTCDATE()
);

---

-- 4. Bảng `companies`: Thông tin các công ty
CREATE TABLE companies (
    company_id INT PRIMARY KEY IDENTITY(1,1),
    name VARCHAR(255) NOT NULL,
    description NVARCHAR(MAX),
    logo_url VARCHAR(255),
    website VARCHAR(255),
    industry VARCHAR(100),
    location VARCHAR(255),
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    created_by INT REFERENCES users(user_id) -- Người dùng (thường là nhà tuyển dụng/admin) tạo công ty này
);

CREATE INDEX idx_companies_name ON companies(name);

---

-- 5. Bảng `skills`: Danh sách các kỹ năng
CREATE TABLE skills (
    skill_id INT PRIMARY KEY IDENTITY(1,1),
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(100) -- Ví dụ: 'Programming', 'Soft Skills', 'Design'
);

---

-- 6. Bảng `job_categories`: Danh mục cho các vị trí tuyển dụng
CREATE TABLE job_categories (
    category_id INT PRIMARY KEY IDENTITY(1,1),
    category_name VARCHAR(100) UNIQUE NOT NULL,
    description NVARCHAR(MAX)
);

---

-- 7. Bảng `positions`: Thông tin về các vị trí thực tập/tuyển dụng
CREATE TABLE positions (
    position_id INT PRIMARY KEY IDENTITY(1,1),
    company_id INT NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description NVARCHAR(MAX) NOT NULL,
    position_type VARCHAR(50) NOT NULL, -- Ví dụ: 'internship', 'full-time', 'part-time'
    location VARCHAR(255),
    is_remote BIT DEFAULT 0,
    salary_range VARCHAR(100),
    application_deadline DATE,
    is_active BIT DEFAULT 1,
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    created_by INT REFERENCES users(user_id), -- Người dùng (thường là nhà tuyển dụng) tạo vị trí này
    category_id INT REFERENCES job_categories(category_id) -- Liên kết với danh mục công việc
);

CREATE INDEX idx_positions_company ON positions(company_id);
CREATE INDEX idx_positions_type ON positions(position_type);
CREATE INDEX idx_positions_active ON positions(is_active);
CREATE INDEX idx_positions_category ON positions(category_id);

---

-- 8. Bảng `position_skills`: Kỹ năng yêu cầu cho từng vị trí
CREATE TABLE position_skills (
    position_id INT REFERENCES positions(position_id) ON DELETE CASCADE,
    skill_id INT REFERENCES skills(skill_id) ON DELETE CASCADE,
    is_required BIT DEFAULT 1, -- Kỹ năng này có bắt buộc không
    PRIMARY KEY (position_id, skill_id)
);

---

-- 9. Bảng `user_skills`: Kỹ năng của từng người dùng
CREATE TABLE user_skills (
    user_id INT REFERENCES users(user_id) ON DELETE CASCADE,
    skill_id INT REFERENCES skills(skill_id) ON DELETE CASCADE,
    proficiency_level INT CHECK (proficiency_level BETWEEN 1 AND 5), -- Mức độ thành thạo (1-5)
    PRIMARY KEY (user_id, skill_id)
);

---

-- 10. Bảng `applications`: Đơn ứng tuyển của người dùng vào vị trí
CREATE TABLE applications (
    application_id INT PRIMARY KEY IDENTITY(1,1),
    position_id INT NOT NULL REFERENCES positions(position_id) ON DELETE CASCADE,
    user_id INT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    current_status VARCHAR(50) NOT NULL DEFAULT 'applied', -- Ví dụ: 'applied', 'under_review', 'interview', 'offered', 'rejected', 'hired'
    applied_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    cover_letter NVARCHAR(MAX),
    additional_info NVARCHAR(MAX), -- Dùng NVARCHAR(MAX) để lưu JSON cho thông tin bổ sung (ví dụ: câu hỏi tùy chỉnh)
    UNIQUE(position_id, user_id) -- Đảm bảo một người dùng chỉ nộp 1 đơn cho 1 vị trí
);

CREATE INDEX idx_applications_user ON applications(user_id);
CREATE INDEX idx_applications_position ON applications(position_id);
CREATE INDEX idx_applications_status ON applications(current_status);

---

-- 11. Bảng `application_status_history`: Lịch sử thay đổi trạng thái của đơn ứng tuyển
CREATE TABLE application_status_history (
    history_id INT PRIMARY KEY IDENTITY(1,1),
    application_id INT NOT NULL REFERENCES applications(application_id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL, -- Trạng thái mới
    changed_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    changed_by INT REFERENCES users(user_id), -- Người dùng (nhà tuyển dụng/admin) thực hiện thay đổi
    notes NVARCHAR(MAX) -- Ghi chú về lý do thay đổi trạng thái
);

CREATE INDEX idx_status_history_app ON application_status_history(application_id);

---

-- 12. Bảng `position_history`: Lịch sử thay đổi của một vị trí tuyển dụng
CREATE TABLE position_history (
    history_id INT PRIMARY KEY IDENTITY(1,1),
    position_id INT NOT NULL REFERENCES positions(position_id) ON DELETE CASCADE,
    changed_by_user_id INT REFERENCES users(user_id), -- Người dùng thực hiện thay đổi
    changed_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    change_type VARCHAR(50) NOT NULL, -- Ví dụ: 'update_description', 'extend_deadline', 'deactivate'
    old_value NVARCHAR(MAX), -- Lưu giá trị cũ (có thể là JSON của một phần hoặc toàn bộ đối tượng)
    new_value NVARCHAR(MAX),  -- Lưu giá trị mới (có thể là JSON của một phần hoặc toàn bộ đối tượng)
    notes NVARCHAR(MAX)
);

CREATE INDEX idx_position_history_position ON position_history(position_id);
CREATE INDEX idx_position_history_user ON position_history(changed_by_user_id);

---

-- 13. Bảng `company_reviews`: Đánh giá của người dùng về công ty
CREATE TABLE company_reviews (
    review_id INT PRIMARY KEY IDENTITY(1,1),
    user_id INT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- Người dùng (ứng viên) gửi đánh giá
    company_id INT NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
    rating INT CHECK (rating BETWEEN 1 AND 5) NOT NULL, -- Điểm đánh giá từ 1 đến 5 sao
    comment NVARCHAR(MAX),
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    is_approved BIT DEFAULT 0 -- Cần duyệt bởi quản trị viên trước khi hiển thị
);

CREATE INDEX idx_company_reviews_user ON company_reviews(user_id);
CREATE INDEX idx_company_reviews_company ON company_reviews(company_id);

---

-- 14. Bảng `applicant_notes`: Ghi chú nội bộ của nhà tuyển dụng về ứng viên
CREATE TABLE applicant_notes (
    note_id INT PRIMARY KEY IDENTITY(1,1),
    application_id INT NOT NULL REFERENCES applications(application_id) ON DELETE CASCADE,
    interviewer_user_id INT NOT NULL REFERENCES users(user_id), -- Người tạo ghi chú (thường là nhà tuyển dụng)
    note_text NVARCHAR(MAX) NOT NULL,
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE()
);

CREATE INDEX idx_applicant_notes_application ON applicant_notes(application_id);
CREATE INDEX idx_applicant_notes_interviewer ON applicant_notes(interviewer_user_id);

---

-- 15. Bảng `conversations`: Lưu trữ các cuộc hội thoại giữa 2 người dùng
CREATE TABLE conversations (
    conversation_id INT PRIMARY KEY IDENTITY(1,1),
    participant1_user_id INT NOT NULL REFERENCES users(user_id),
    participant2_user_id INT NOT NULL REFERENCES users(user_id),
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    last_message_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    -- Đảm bảo chỉ có một cuộc trò chuyện duy nhất giữa một cặp người dùng,
    -- cần sắp xếp ID người dùng để tránh trùng lặp (ví dụ: (1,2) và (2,1) là như nhau)
    UNIQUE (participant1_user_id, participant2_user_id) -- Có thể cần CHECK constraint để đảm bảo participant1_user_id < participant2_user_id nếu muốn chuẩn hóa hoàn toàn UNIQUE
);

CREATE INDEX idx_conversations_p1 ON conversations(participant1_user_id);
CREATE INDEX idx_conversations_p2 ON conversations(participant2_user_id);

---

-- 16. Bảng `messages`: Lưu trữ nội dung tin nhắn trong các cuộc hội thoại
CREATE TABLE messages (
    message_id INT PRIMARY KEY IDENTITY(1,1),
    conversation_id INT NOT NULL REFERENCES conversations(conversation_id) ON DELETE CASCADE,
    sender_user_id INT NOT NULL REFERENCES users(user_id),
    message_text NVARCHAR(MAX) NOT NULL,
    sent_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    is_read BIT DEFAULT 0 -- Trạng thái đã đọc/chưa đọc
);

CREATE INDEX idx_messages_conversation ON messages(conversation_id);
CREATE INDEX idx_messages_sender ON messages(sender_user_id);

---

-- 17. Bảng `notifications`: Lưu trữ thông báo gửi đến người dùng
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY IDENTITY(1,1),
    user_id INT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message NVARCHAR(MAX) NOT NULL,
    is_read BIT DEFAULT 0,
    created_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    related_entity_type VARCHAR(50), -- Ví dụ: 'application', 'position', 'conversation', 'review'
    related_entity_id INT, -- ID của thực thể liên quan (ví dụ: application_id, position_id, conversation_id)
    notification_type VARCHAR(50) -- Ví dụ: 'status_change', 'new_position', 'new_message', 'new_review'
);

CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);

---

-- 18. Bảng `websocket_connections`: Quản lý các kết nối WebSocket đang hoạt động
CREATE TABLE websocket_connections (
    connection_id VARCHAR(255) PRIMARY KEY, -- ID duy nhất cho mỗi kết nối WebSocket
    user_id INT NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    connected_at DATETIMEOFFSET DEFAULT GETUTCDATE(),
    last_activity DATETIMEOFFSET DEFAULT GETUTCDATE(),
    client_info NVARCHAR(MAX) -- Thông tin client (ví dụ: IP, User-Agent) dưới dạng JSON
);

CREATE INDEX idx_websocket_user ON websocket_connections(user_id);