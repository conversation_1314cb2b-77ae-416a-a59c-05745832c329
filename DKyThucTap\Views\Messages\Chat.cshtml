﻿@{
    ViewData["Title"] = "Trao đổi với nhà tuyển dụng";
}
<div class="card shadow-sm">
    <div class="card-header bg-primary text-white">
        <i class="fas fa-comments me-2"></i>
        Trao đổi với @ViewBag.EmployerName về "@ViewBag.PositionTitle"
    </div>
    <div class="card-body" id="chatBox" style="height: 400px; overflow-y: auto; background-color: #f8f9fa;">
        <div id="messagesList" class="d-flex flex-column"></div>
    </div>
    <div class="card-footer">
        <div class="input-group">
            <input type="text" id="messageInput" class="form-control" placeholder="Nhập tin nhắn..." />
            <button class="btn btn-success" id="sendButton">
                <i class="fas fa-paper-plane"></i> <PERSON><PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.0/signalr.min.js"></script>
    <script>
        const userId = '@User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value';
        const conversationId = parseInt('@ViewBag.ConversationId'); // ép kiểu số nguyên
        let connectionStarted = false;

        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/chathub")
            .build();

        // Hàm scroll xuống cuối chat
        function scrollToBottom() {
            const chatBox = document.getElementById("chatBox");
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        // Nhận tin nhắn realtime
        connection.on("ReceiveMessage", function (senderId, message, time) {
            const alignment = senderId == userId ? "text-end" : "text-start";
            const bubbleClass = senderId == userId ? "bg-success text-white" : "bg-light";
            const msgHtml = `
                <div class="mb-2 ${alignment}">
                    <div class="d-inline-block p-2 rounded ${bubbleClass}" style="max-width:70%;">
                        ${message}
                        <div class="small text-muted">${time}</div>
                    </div>
                </div>`;
            document.getElementById("messagesList").innerHTML += msgHtml;
            scrollToBottom();
        });

        // Gửi tin nhắn
        async function sendMessage() {
            const input = document.getElementById("messageInput");
            const message = input.value.trim();
            if (!message) return;

            if (!connectionStarted) {
                console.warn("⚠️ Kết nối chưa sẵn sàng!");
                return;
            }

            try {
                await connection.invoke("SendMessage", conversationId, message);
                input.value = "";
            } catch (err) {
                console.error("❌ Lỗi khi gửi tin nhắn:", err);
            }
        }

        // Sự kiện nút Gửi
        document.getElementById("sendButton").addEventListener("click", sendMessage);

        // Nhấn Enter để gửi
        document.getElementById("messageInput").addEventListener("keypress", function (e) {
            if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Kết nối & load tin nhắn cũ
        connection.start().then(() => {
            connectionStarted = true;
            console.log("✅ ChatHub connected!");

            fetch(`/Messages/GetMessages?conversationId=${conversationId}`)
                .then(res => res.json())
                .then(messages => {
                    const list = document.getElementById("messagesList");
                    list.innerHTML = "";
                    messages.forEach(m => {
                        const alignment = m.senderUserId == userId ? "text-end" : "text-start";
                        const bubbleClass = m.senderUserId == userId ? "bg-success text-white" : "bg-light";
                        const msgHtml = `
                            <div class="mb-2 ${alignment}">
                                <div class="d-inline-block p-2 rounded ${bubbleClass}" style="max-width:70%;">
                                    ${m.messageText}
                                    <div class="small text-muted">${m.sentAt}</div>
                                </div>
                            </div>`;
                        list.innerHTML += msgHtml;
                    });
                    scrollToBottom();
                });
        }).catch(err => console.error("❌ Lỗi kết nối:", err));
    </script>
}
