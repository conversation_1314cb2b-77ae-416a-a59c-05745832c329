﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@ViewData["Title"] - Internship Admin</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <link rel="stylesheet" href="~/css/admin_custom.css" />

    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">

        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a asp-area="Admin" asp-controller="AdminHome" asp-action="AdminDashboard" class="nav-link">Dashboard</a>
                </li>
            </ul>

            <ul class="navbar-nav ml-auto">
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="fas fa-user-shield"></i>
                        <span class="d-none d-md-inline">@User.Identity.Name</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                        <span class="dropdown-item dropdown-header">Tài khoản Admin</span>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user-cog mr-2"></i> Hồ sơ
                        </a>
                        <div class="dropdown-divider"></div>
                        <form asp-area="" asp-controller="Auth" asp-action="Logout" method="post" id="logoutForm">
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="fas fa-sign-out-alt mr-2"></i> Đăng xuất
                            </button>
                        </form>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>
            </ul>
        </nav>

        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <a asp-area="Admin" asp-controller="AdminHome" asp-action="AdminDashboard" class="brand-link">
                <img src="~/images/admin-logo.png" alt="Admin Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
                <span class="brand-text font-weight-light">Internship Admin</span>
            </a>

            <div class="sidebar">
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="AdminHome" asp-action="AdminDashboard" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>Dashboard</p>
                            </a>
                        </li>

                        <li class="nav-header">QUẢN LÝ HỆ THỐNG</li>

                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="Users" asp-action="Index" class="nav-link">
                                <i class="nav-icon fas fa-users"></i>
                                <p>
                                    Quản lý người dùng
                                    <span class="badge badge-info right">New</span>
                                </p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-building"></i>
                                <p>
                                    Quản lý công ty
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a asp-area="Admin" asp-controller="Companies" asp-action="Index" class="nav-link">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>Danh sách công ty</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Admin" asp-controller="Companies" asp-action="ViolationReports" class="nav-link">
                                        <i class="far fa-circle nav-icon text-warning"></i>
                                        <p>Báo cáo vi phạm</p>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="Positions" asp-action="Index" class="nav-link">
                                <i class="nav-icon fas fa-briefcase"></i>
                                <p>Vị trí tuyển dụng</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="Applications" asp-action="Index" class="nav-link">
                                <i class="nav-icon fas fa-file-alt"></i>
                                <p>Đơn ứng tuyển</p>
                            </a>
                        </li>

                        @* <li class="nav-item">
                            <a asp-area="Admin" asp-controller="Reviews" asp-action="Index" class="nav-link">
                                <i class="nav-icon fas fa-star"></i>
                                <p>Đánh giá công ty</p>
                            </a>
                        </li> *@

                        <li class="nav-header">THỐNG KÊ & BÁO CÁO</li>

                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="Reports" asp-action="Index" class="nav-link">
                                <i class="nav-icon fas fa-chart-pie"></i>
                                <p>Báo cáo tổng quan</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a asp-area="Admin" asp-controller="AdminHome" asp-action="AdminDashboard" class="nav-link">
                                <i class="nav-icon fas fa-chart-line"></i>
                                <p>Thống kê hệ thống</p>
                            </a>
                        </li>

                        <li class="nav-header">TÀI KHOẢN</li>

                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fas fa-user-cog"></i>
                                <p>Cài đặt tài khoản</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <form asp-area="" asp-controller="Auth" asp-action="Logout" method="post" style="margin: 0;">
                                <button type="submit" class="nav-link btn btn-link text-left w-100 text-danger" style="border: none; background: none;">
                                    <i class="nav-icon fas fa-sign-out-alt"></i>
                                    <p>Đăng xuất</p>
                                </button>
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <div class="content-wrapper">
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">@ViewData["Title"]</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-right">
                                <li class="breadcrumb-item">
                                    <a asp-area="Admin" asp-controller="AdminHome" asp-action="AdminDashboard">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item active">@ViewData["Title"]</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <section class="content">
                <div class="container-fluid">
                    <!-- Alert messages -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @if (TempData["WarningMessage"] != null)
                    {
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            @TempData["WarningMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @if (TempData["InfoMessage"] != null)
                    {
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle"></i>
                            @TempData["InfoMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @RenderBody()
                </div>
            </section>
        </div>

        <footer class="main-footer">
            
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

    <script>
        $(document).ready(function() {
            // Auto dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Highlight active menu item
            var currentPath = window.location.pathname.toLowerCase();
            $('.nav-sidebar .nav-link').each(function() {
                var linkPath = $(this).attr('href');
                if (linkPath && currentPath.includes(linkPath.toLowerCase())) {
                    $(this).addClass('active');
                    $(this).parents('.nav-item').addClass('menu-open');
                }
            });

            console.log('Admin layout loaded successfully');
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>