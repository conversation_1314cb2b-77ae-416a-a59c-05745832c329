@model DKyThucTap.Models.ViewModels.ApplicationDetailViewModel
@{
    ViewData["Title"] = $"Chi tiết ứng viên - {Model.Application.ApplicantName}";
}

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>Chi tiết ứng viên</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Trang chủ</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("ByPosition", new { positionId = Model.Application.PositionId })">Quản lý ứng viên</a></li>
                            <li class="breadcrumb-item active">@Model.Application.ApplicantName</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="@Url.Action("ByPosition", new { positionId = Model.Application.PositionId })" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Quay lại danh sách
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Applicant Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Thông tin ứng viên</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            @if (!string.IsNullOrEmpty(Model.Application.ApplicantProfilePictureUrl))
                            {
                                <img src="@Model.Application.ApplicantProfilePictureUrl" alt="@Model.Application.ApplicantName" 
                                     class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                     style="width: 120px; height: 120px;">
                                    <i class="fas fa-user fa-3x text-white"></i>
                                </div>
                            }
                            <h5>@Model.Application.ApplicantName</h5>
                            <p class="text-muted">@Model.Application.ApplicantEmail</p>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Thông tin liên hệ</h6>
                                    <p><i class="fas fa-envelope me-2"></i>@Model.Application.ApplicantEmail</p>
                                    @if (!string.IsNullOrEmpty(Model.Application.ApplicantPhone))
                                    {
                                        <p><i class="fas fa-phone me-2"></i>@Model.Application.ApplicantPhone</p>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.Application.ApplicantAddress))
                                    {
                                        <p><i class="fas fa-map-marker-alt me-2"></i>@Model.Application.ApplicantAddress</p>
                                    }
                                </div>
                                <div class="col-md-6">
                                    <h6>Tài liệu</h6>
                                    @if (!string.IsNullOrEmpty(Model.Application.ApplicantCvUrl))
                                    {
                                        <p>
                                            <a href="@Model.Application.ApplicantCvUrl" target="_blank" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-file-pdf me-1"></i>Xem CV
                                            </a>
                                        </p>
                                    }
                                    else
                                    {
                                        <p class="text-muted">Chưa có CV</p>
                                    }
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.Application.ApplicantBio))
                            {
                                <div class="mt-3">
                                    <h6>Giới thiệu bản thân</h6>
                                    <p>@Model.Application.ApplicantBio</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skills -->
            @if (Model.Application.ApplicantSkills.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Kỹ năng</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var skill in Model.Application.ApplicantSkills)
                            {
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>@skill.SkillName</span>
                                        @if (skill.ProficiencyLevel.HasValue)
                                        {
                                            <div class="progress" style="width: 100px; height: 8px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: @(skill.ProficiencyLevel * 20)%" 
                                                     aria-valuenow="@skill.ProficiencyLevel" 
                                                     aria-valuemin="0" aria-valuemax="5"></div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Cover Letter -->
            @if (!string.IsNullOrEmpty(Model.Application.CoverLetter))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Thư xin việc</h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-3 rounded">
                            @Html.Raw(Model.Application.CoverLetter.Replace("\n", "<br>"))
                        </div>
                    </div>
                </div>
            }

            <!-- Additional Info -->
            @if (!string.IsNullOrEmpty(Model.Application.AdditionalInfo))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Thông tin bổ sung</h5>
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-3 rounded">
                            @Html.Raw(Model.Application.AdditionalInfo.Replace("\n", "<br>"))
                        </div>
                    </div>
                </div>
            }

            <!-- Notes -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Ghi chú đánh giá</h5>
                    @if (Model.CanAddNotes)
                    {
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addNoteModal">
                            <i class="fas fa-plus me-1"></i>Thêm ghi chú
                        </button>
                    }
                </div>
                <div class="card-body">
                    @if (Model.Application.Notes.Any())
                    {
                        @foreach (var note in Model.Application.Notes)
                        {
                            <div class="border-bottom pb-3 mb-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>@note.InterviewerName</strong>
                                        <small class="text-muted ms-2">@note.CreatedAt?.ToString("dd/MM/yyyy HH:mm")</small>
                                    </div>
                                </div>
                                <p class="mt-2 mb-0">@note.NoteText</p>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted mb-0">Chưa có ghi chú nào</p>
                    }
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Position Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Thông tin vị trí</h5>
                </div>
                <div class="card-body">
                    <h6>@Model.Position.Title</h6>
                    <p class="text-muted mb-2">@Model.Position.CompanyName</p>
                    @if (!string.IsNullOrEmpty(Model.Position.Location))
                    {
                        <p class="mb-2"><i class="fas fa-map-marker-alt me-1"></i>@Model.Position.Location</p>
                    }
                    <a href="@Url.Action("Details", "Position", new { id = Model.Position.PositionId })" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>Xem chi tiết vị trí
                    </a>
                </div>
            </div>

            <!-- Application Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Trạng thái ứng tuyển</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <span class="badge @GetStatusBadgeClass(Model.Application.CurrentStatus) fs-6">
                            @GetStatusDisplayName(Model.Application.CurrentStatus)
                        </span>
                    </div>
                    <p class="text-muted text-center mb-3">
                        Ứng tuyển lúc: @Model.Application.AppliedAt?.ToString("dd/MM/yyyy HH:mm")
                    </p>
                    
                    @if (Model.CanManageApplication)
                    {
                        <form asp-action="UpdateStatus" method="post">
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="ApplicationId" value="@Model.Application.ApplicationId" />
                            
                            <div class="mb-3">
                                <label class="form-label">Thay đổi trạng thái</label>
                                <select name="NewStatus" class="form-select" required>
                                    <option value="">Chọn trạng thái mới</option>
                                    @foreach (var status in Model.AvailableStatuses)
                                    {
                                        @if (status != Model.Application.CurrentStatus)
                                        {
                                            <option value="@status">@GetStatusDisplayName(status)</option>
                                        }
                                    }
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Ghi chú (tùy chọn)</label>
                                <textarea name="Notes" class="form-control" rows="3" placeholder="Lý do thay đổi trạng thái..."></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-1"></i>Cập nhật trạng thái
                            </button>
                        </form>
                    }
                </div>
            </div>

            <!-- Status History -->
            @if (Model.Application.StatusHistory.Any())
            {
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Lịch sử trạng thái</h5>
                    </div>
                    <div class="card-body">
                        @foreach (var history in Model.Application.StatusHistory)
                        {
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 32px; height: 32px;">
                                        <i class="fas fa-clock text-white" style="font-size: 12px;"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <span class="badge @GetStatusBadgeClass(history.Status)">
                                                @GetStatusDisplayName(history.Status)
                                            </span>
                                        </div>
                                        <small class="text-muted">@history.ChangedAt?.ToString("dd/MM HH:mm")</small>
                                    </div>
                                    <small class="text-muted">Bởi: @history.ChangedByName</small>
                                    @if (!string.IsNullOrEmpty(history.Notes))
                                    {
                                        <div class="small mt-1">@history.Notes</div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Add Note Modal -->
@if (Model.CanAddNotes)
{
    <div class="modal fade" id="addNoteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form asp-action="AddNote" method="post">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="ApplicationId" value="@Model.Application.ApplicationId" />
                    
                    <div class="modal-header">
                        <h5 class="modal-title">Thêm ghi chú đánh giá</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Nội dung ghi chú</label>
                            <textarea name="NoteText" class="form-control" rows="4" required 
                                      placeholder="Nhập đánh giá, nhận xét về ứng viên..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Lưu ghi chú
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
}

@functions {
    string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "applied" => "bg-primary",
            "reviewing" => "bg-warning",
            "interviewed" => "bg-info",
            "accepted" => "bg-success",
            "rejected" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(string status)
    {
        return status switch
        {
            "applied" => "Đã ứng tuyển",
            "reviewing" => "Đang xem xét",
            "interviewed" => "Đã phỏng vấn",
            "accepted" => "Đã chấp nhận",
            "rejected" => "Đã từ chối",
            _ => status
        };
    }
}
