# Hướng dẫn tích hợp Notification System

## 📋 Tổng quan

Hệ thống thông báo real-time đã được tích hợp hoàn toàn vào các CRUD operations của các entity chính trong ứng dụng DKyThucTap. Điều này tạo ra trải nghiệm tương tác như mạng xã hội, nơi người dùng được thông báo ngay lập tức về các thay đổi liên quan.

## 🔧 Các tích hợp đã hoàn thành

### 1. **Position Management (Quản lý vị trí tuyển dụng)**

#### PositionService Integration:
- ✅ **Tạo position mới**: Tự động tìm và thông báo cho candidates phù hợp
- ✅ **Cập nhật position**: Thông báo cho applicants về thay đổi quan trọng
- ✅ **Thay đổi trạng thái**: Thông báo khi position được kích hoạt/tạm dừng
- ✅ **Xóa position**: Thông báo cho tất cả applicants

#### Notification Types:
```csharp
// Thông báo job mới phù hợp
await _notificationIntegration.NotifyNewJobMatchingCriteriaAsync(
    candidateId, jobTitle, positionId, "Phù hợp với kỹ năng và vị trí của bạn"
);

// Thông báo cập nhật job
await _notificationIntegration.NotifyJobStatusUpdateAsync(
    applicantId, jobTitle, positionId, "Thông tin công việc đã được cập nhật"
);
```

#### Candidate Matching Algorithm:
- Tìm candidates dựa trên **skills** (UserSkills)
- Tìm candidates dựa trên **location** (UserProfile.Address)
- Loại trừ candidates đã apply
- Giới hạn 50 notifications để tránh spam

### 2. **Company Management (Quản lý công ty)**

#### CompanyService Integration:
- ✅ **Tạo company mới**: Thông báo admins để review và approval
- ✅ **Cập nhật company profile**: Thông báo tất cả recruiters trong company
- ✅ **Approve/Reject recruiter requests**: Thông báo user và team members

#### Notification Types:
```csharp
// Thông báo đăng ký company mới cho admin
await _notificationIntegration.NotifyCompanyRegistrationAsync(
    adminId, companyName, companyId, "Cần xem xét và phê duyệt"
);

// Thông báo cập nhật profile company
await _notificationIntegration.NotifyCompanyProfileUpdatedAsync(
    recruiterId, companyName, companyId
);

// Thông báo member mới join company
await _notificationIntegration.NotifyNewCompanyFollowerAsync(
    recruiterId, newMemberName, companyId
);
```

### 3. **User Management (Quản lý người dùng)**

#### AuthService Integration:
- ✅ **Đăng nhập**: Security alert notification
- ✅ **Đăng ký**: Welcome notification với hướng dẫn
- ✅ **Cập nhật profile**: Confirmation notification

#### Notification Types:
```csharp
// Security alert khi login
await _notificationIntegration.NotifySecurityAlertAsync(
    userId, "Đăng nhập thành công", 
    $"Tài khoản {email} vừa đăng nhập vào hệ thống lúc {DateTime.Now}"
);

// Welcome message cho user mới
await _notificationIntegration.NotifyAccountVerificationAsync(
    userId, $"Chào mừng {firstName} đến với hệ thống tuyển dụng!"
);
```

## 🚀 Cách hoạt động

### 1. **Dependency Injection**
Tất cả services đã được cập nhật để inject `INotificationIntegrationService`:

```csharp
public PositionService(
    DKyThucTapContext context, 
    ILogger<PositionService> logger,
    INotificationIntegrationService notificationIntegration)
{
    _context = context;
    _logger = logger;
    _notificationIntegration = notificationIntegration;
}
```

### 2. **Error Handling**
Tất cả notification calls được wrap trong try-catch để không ảnh hưởng core functionality:

```csharp
try
{
    await _notificationIntegration.NotifyNewJobMatchingCriteriaAsync(...);
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error sending notifications");
    // Core operation continues normally
}
```

### 3. **Real-time Delivery**
- Notifications được gửi qua SignalR Hub
- Chỉ gửi cho users đang online
- Offline users sẽ thấy notifications khi login lại

## 📊 Performance Considerations

### 1. **Async Operations**
- Tất cả notification calls là async
- Không block main thread
- Core operations hoàn thành trước khi gửi notifications

### 2. **Batch Processing**
- Candidate matching giới hạn 50 users
- Bulk notifications cho multiple users
- Efficient database queries

### 3. **Logging**
- Comprehensive logging cho debugging
- Track notification success/failure
- Performance monitoring

## 🔍 Testing Guide

### 1. **Position Notifications**
```bash
# Test tạo position mới
POST /Position/Create
# Kiểm tra: Candidates phù hợp nhận notification

# Test cập nhật position
PUT /Position/Edit/{id}
# Kiểm tra: Applicants nhận notification về thay đổi

# Test thay đổi status
POST /Position/UpdateStatus/{id}
# Kiểm tra: Applicants nhận notification về status
```

### 2. **Company Notifications**
```bash
# Test tạo company mới
POST /Company/Create
# Kiểm tra: Admins nhận notification

# Test cập nhật company
PUT /Company/Edit/{id}
# Kiểm tra: Company recruiters nhận notification

# Test approve/reject recruiter
POST /Company/RespondToRequest
# Kiểm tra: User và team members nhận notification
```

### 3. **User Notifications**
```bash
# Test đăng nhập
POST /Auth/Login
# Kiểm tra: User nhận security alert

# Test đăng ký
POST /Auth/Register
# Kiểm tra: User nhận welcome message

# Test cập nhật profile
POST /Account/Profile
# Kiểm tra: User nhận confirmation
```

## 🎯 Benefits Achieved

### 1. **Social Media-like Experience**
- Real-time notifications như Facebook
- Instant feedback cho user actions
- Enhanced user engagement

### 2. **Business Value**
- Candidates biết ngay về jobs phù hợp
- Recruiters được update về company changes
- Admins được notify về registrations cần review

### 3. **Technical Excellence**
- Clean separation of concerns
- Non-blocking notification system
- Comprehensive error handling
- Scalable architecture

## 📝 Next Steps

1. **Monitor Performance**: Track notification delivery rates
2. **User Feedback**: Collect feedback về notification relevance
3. **Optimization**: Fine-tune candidate matching algorithm
4. **Analytics**: Add notification analytics dashboard

---

**Tác giả**: AI Assistant  
**Ngày tạo**: 2025-08-04  
**Version**: 1.0
