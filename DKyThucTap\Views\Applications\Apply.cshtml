﻿@model DKyThucTap.Models.Position
@{
    ViewData["Title"] = "Ứng tuyển công việc";
}

<div class="container mt-5">
    <!-- Hero Section -->
    <div class="text-center mb-4">
        <h2 class="fw-bold text-primary">
            <i class="fas fa-paper-plane"></i> Ứng tuyển:
            <span class="text-success">@Model.Title</span>
        </h2>
        <p class="text-muted">
            Hãy chắc chắn rằng thông tin cá nhân và CV của bạn đã được cập nhật trước khi nộp đơn.
        </p>
        <div class="alert alert-info shadow-sm w-75 mx-auto">
            <i class="fas fa-info-circle"></i>
            Nhà tuyển dụng thường ưu tiên hồ sơ có CV chi tiết và thư xin việc đư<PERSON> viết riêng cho vị trí này.
        </div>
    </div>

    <div class="row">
        <!-- Thông tin công việc -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-lg h-100">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-briefcase"></i> Thông tin công việc
                </div>
                <div class="card-body">
                    <h4 class="text-dark">@Model.Title</h4>
                    <p class="fw-semibold">@Model.Company?.Name - <span class="text-muted">@Model.Company?.Industry</span></p>

                    <ul class="list-unstyled mb-4">
                        <li><i class="fas fa-map-marker-alt text-danger"></i> Địa điểm: @Model.Location</li>
                        <li><i class="fas fa-calendar-alt text-info"></i> Hạn: @Model.ApplicationDeadline?.ToString("dd/MM/yyyy")</li>
                        <li><i class="fas fa-user-tie text-success"></i> Loại: @Model.PositionType</li>
                        <li><i class="fas fa-dollar-sign text-warning"></i> Mức lương: @Model.SalaryRange</li>
                    </ul>

                    <h5 class="mt-3"><i class="fas fa-tools"></i> Kỹ năng yêu cầu</h5>
                    <div class="mb-3">
                        @if (Model.PositionSkills?.Any() == true)
                        {
                            foreach (var skill in Model.PositionSkills)
                            {
                                <span class="badge bg-secondary me-1">@skill.Skill?.Name</span>
                            }
                        }
                        else
                        {
                            <span class="text-muted">Không yêu cầu kỹ năng cụ thể</span>
                        }
                    </div>

                    <h5><i class="fas fa-file-alt"></i> Mô tả công việc</h5>
                    <p>@Model.Description</p>
                </div>
            </div>
        </div>

        <!-- Form ứng tuyển -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-lg h-100">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-edit"></i> Nộp đơn ứng tuyển
                </div>
                <div class="card-body">
                    <form asp-action="Apply" asp-controller="Applications" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="positionId" value="@Model.PositionId" />

                        <div class="mb-3">
                            <label for="coverLetter" class="form-label fw-semibold">
                                <i class="fas fa-envelope-open-text text-success"></i> Thư xin việc
                            </label>
                            <textarea name="coverLetter" class="form-control" rows="6" placeholder="Hãy viết vài dòng thuyết phục nhà tuyển dụng..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="cvFile" class="form-label fw-semibold">
                                <i class="fas fa-file-upload text-primary"></i> Upload CV (PDF, DOC, DOCX)
                            </label>
                            <input type="file" name="cvFile" class="form-control" accept=".pdf,.doc,.docx" />
                            <small class="text-muted">
                                Đặt tên file chuyên nghiệp, ví dụ: <strong>NguyenVanAn_CV.pdf</strong>
                            </small>
                        </div>

                        <button type="submit" class="btn btn-success me-2">
                            <i class="fas fa-paper-plane"></i> Nộp đơn
                        </button>
                        <a asp-controller="Position" asp-action="Details" asp-route-id="@Model.PositionId"
                           class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
