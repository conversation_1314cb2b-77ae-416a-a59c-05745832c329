@model DKyThucTap.Models.DTOs.RegisterDto
@{
    ViewData["Title"] = "Đăng ký";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>Đăng ký tài kho<PERSON>n
                </h4>
            </div>
            <div class="card-body">
                <form asp-action="Register" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="FirstName" class="form-label">
                                <i class="fas fa-user me-1"></i>Tên <span class="text-danger">*</span>
                            </label>
                            <input asp-for="FirstName" class="form-control" placeholder="Nhập tên của bạn" />
                            <span asp-validation-for="FirstName" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="LastName" class="form-label">
                                <i class="fas fa-user me-1"></i>Họ <span class="text-danger">*</span>
                            </label>
                            <input asp-for="LastName" class="form-control" placeholder="Nhập họ của bạn" />
                            <span asp-validation-for="LastName" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                        </label>
                        <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Phone" class="form-label">
                            <i class="fas fa-phone me-1"></i>Số điện thoại
                        </label>
                        <input asp-for="Phone" class="form-control" placeholder="Nhập số điện thoại" />
                        <span asp-validation-for="Phone" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="RoleId" class="form-label">
                            <i class="fas fa-user-tag me-1"></i>Vai trò <span class="text-danger">*</span>
                        </label>
                        <select asp-for="RoleId" class="form-select">
                            <option value="">-- Chọn vai trò --</option>
                            @if (ViewBag.Roles != null)
                            {
                                @foreach (var role in ViewBag.Roles as List<DKyThucTap.Models.Role>)
                                {
                                    <option value="@role.RoleId">@role.RoleName</option>
                                }
                            }
                            else
                            {
                                <option value="" disabled>Không thể tải danh sách vai trò</option>
                            }
                        </select>
                        <span asp-validation-for="RoleId" class="text-danger"></span>
                        @if (ViewBag.Roles == null || ((List<DKyThucTap.Models.Role>)ViewBag.Roles).Count == 0)
                        {
                            <div class="alert alert-warning mt-2">
                                <small><i class="fas fa-exclamation-triangle me-1"></i>Không thể tải danh sách vai trò. Vui lòng thử lại sau.</small>
                            </div>
                        }
                        <div class="form-text">
                            <small>
                                <strong>Candidate:</strong> Tìm kiếm và ứng tuyển việc làm<br>
                                <strong>Recruiter:</strong> Đăng tin tuyển dụng và quản lý ứng viên
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Mật khẩu <span class="text-danger">*</span>
                            </label>
                            <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                            <div class="form-text">
                                <small>Mật khẩu phải có ít nhất 6 ký tự, bao gồm chữ hoa, chữ thường và số</small>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="ConfirmPassword" class="form-label">
                                <i class="fas fa-lock me-1"></i>Xác nhận mật khẩu <span class="text-danger">*</span>
                            </label>
                            <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-user-plus me-2"></i>Đăng ký
                        </button>
                    </div>
                </form>

                <hr>
                
                <div class="text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a asp-action="Login" class="text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập ngay
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
