﻿@model DKyThucTap.ViewModels.RecruiterProfileViewModel

@{
    ViewData["Title"] = "<PERSON><PERSON> sơ Nhà tuyển dụng";
}

<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />

<div class="container my-5">
    <div class="card shadow-lg border-0 mx-auto" style="max-width: 900px;">
        <!-- Header -->
        <div class="card-header bg-gradient text-white text-center" style="background: linear-gradient(90deg, #007bff, #00c6ff);">
            <h3 class="mb-0"><i class="fas fa-user-tie"></i> <PERSON><PERSON> sơ Nhà tuyển dụng</h3>
        </div>

        <!-- Body -->
        <div class="card-body text-center">
            <!-- Avatar -->
            <img src="~/@(string.IsNullOrEmpty(Model.ProfilePictureUrl)
                                         ? Model.ProfilePictureUrl
                                         : "/images/profiles/default-avatar.png")"
                 class="rounded-circle shadow mb-3 border border-3 border-light"
                 width="130" height="130" />

            <!-- Thông tin cơ bản -->
            <h4 class="fw-bold text-primary">@Model.FullName</h4>
            <p class="mb-1"><i class="fas fa-envelope text-danger"></i> <strong>Email:</strong> @Model.Email</p>
            <p class="mb-1"><i class="fas fa-phone text-success"></i> <strong>Điện thoại:</strong> @(Model.Phone ?? "Chưa cập nhật")</p>
            <p class="mb-3"><i class="fas fa-info-circle text-info"></i> <strong>Giới thiệu:</strong> @(Model.Bio ?? "Chưa có giới thiệu")</p>

            <hr class="my-4" />

            <!-- Danh sách công ty -->
            <h5 class="fw-bold mb-3 text-dark"><i class="fas fa-building"></i> Công ty quản lý</h5>
            @if (Model.Companies.Any())
            {
                <div class="list-group mb-4">
                    @foreach (var company in Model.Companies)
                    {
                        <a href="@Url.Action("Employer", "Profile", new { companyId = company.CompanyId })"
                           class="list-group-item list-group-item-action d-flex justify-content-between align-items-center shadow-sm">
                            <span><i class="fas fa-industry text-secondary"></i> @company.Name</span>
                            <i class="fas fa-arrow-right text-primary"></i>
                        </a>
                    }
                </div>
            }
            else
            {
                <p class="text-muted fst-italic">Nhà tuyển dụng này chưa tham gia công ty nào.</p>
            }

            <!-- Các vị trí đã đăng -->
            <h5 class="fw-bold mb-3 text-dark"><i class="fas fa-briefcase"></i> Các vị trí đã đăng</h5>
            @if (Model.PostedPositions.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover align-middle shadow-sm">
                        <thead class="table-primary">
                            <tr>
                                <th>Chức danh</th>
                                <th>Công ty</th>
                                <th>Trạng thái</th>
                                <th>Ngày đăng</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var pos in Model.PostedPositions)
                            {
                                <tr>
                                    <td><i class="fas fa-briefcase text-success"></i> @pos.Title</td>
                                    <td>@pos.CompanyName</td>
                                    <td>
                                        <span class="badge @(pos.IsActive ? "bg-success" : "bg-secondary")">
                                            @(pos.IsActive ? "Đang tuyển" : "Đã đóng")
                                        </span>
                                    </td>
                                    <td>@pos.CreatedAt.ToLocalTime().ToString("dd/MM/yyyy")</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <p class="text-muted fst-italic">Nhà tuyển dụng này chưa đăng vị trí nào.</p>
            }

            <hr class="my-4" />

            <!-- Quay lại -->
            <div class="text-center">
                <a href="javascript:history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
            </div>
        </div>
    </div>
</div>
