@model DKyThucTap.Models.DTOs.LoginDto
@{
    ViewData["Title"] = "Đăng nhập";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>Đ<PERSON>ng nhập
                </h4>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        @TempData["SuccessMessage"]
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                <form asp-action="Login" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    
                    <input type="hidden" name="ReturnUrl" value="@ViewData["ReturnUrl"]" />
                    
                    <div class="mb-3">
                        <label asp-for="Email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email
                        </label>
                        <input asp-for="Email" class="form-control" placeholder="Nhập email của bạn" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Mật khẩu
                        </label>
                        <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>

                    <div class="mb-3 form-check">
                        <input asp-for="RememberMe" class="form-check-input" />
                        <label asp-for="RememberMe" class="form-check-label">
                            Ghi nhớ đăng nhập
                        </label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                        </button>
                    </div>
                </form>

                <hr>
                
                <div class="text-center">
                    <p class="mb-0">
                        Chưa có tài khoản? 
                        <a asp-action="Register" class="text-decoration-none">
                            <i class="fas fa-user-plus me-1"></i>Đăng ký ngay
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
